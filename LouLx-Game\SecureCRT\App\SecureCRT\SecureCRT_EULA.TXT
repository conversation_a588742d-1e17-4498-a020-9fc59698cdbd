End-User License Agreement for SecureCRT 7.1 ("Software")
Copyright (c) 1995-2013 VanDyke Software, Inc.
All Rights Reserved.

AGREEMENT. After reading this agreement carefully, if you ("Customer") do not agree to all of the terms of this agreement, you may not use this Software. Unless you have a different license agreement signed by VanDyke Software, Inc. that covers this copy of the Software, your use of this Software indicates your acceptance of this license agreement and warranty. All updates to the Software shall be considered part of the Software and subject to the terms of this Agreement. Changes to this Agreement may accompany updates to the Software, in which case by installing such update, Customer accepts the terms of the Agreement as changed. The Agreement is not otherwise subject to addition, amendment, modification, or exception unless in writing signed by an officer of both Customer and VanDyke Software, Inc.

This Software is owned by VanDyke Software, Inc. and is protected by national copyright laws and international copyright treaties.

1. EXPORT LAW. This Software is subject to export control. The Software may be transmitted, exported, or re-exported only under applicable export laws and restrictions and regulations of the United States Bureau of Industry and Security or foreign agencies or authorities. By downloading or using the Software, you are agreeing to comply with export controls. The Software may not be downloaded or otherwise exported or re-exported to any country subject to U.S. trade sanctions governing the Software, sanctioned countries including Cuba, Iran, North Korea, Sudan, and Syria, or by citizens or residents of such countries except citizens who are lawful permanent residents of countries not subject to such sanctions, or by anyone on the U.S. Treasury Department's list of Specially Designated Nationals and Blocked Persons or the U.S. Commerce Department's Table of Denial Orders.

2. GRANT OF LICENSE AND PROHIBITIONS. This Software is licensed to you. You are not obtaining title to the Software or any copyrights. You may not sublicense, rent, lease, convey, modify, translate, convert to another programming language, decompile, or disassemble the Software for any purpose. The license may be transferred to another individual (not resold) if you keep no copies of the Software. Permission must be obtained before mirroring or redistributing the evaluation copies of the Software.

3. USE AND EVALUATION PERIOD. You may use one copy of this Software on one client computer. A copy of this Software is considered in use when loaded into temporary memory (i.e., RAM) and/or installed on a permanent storage device (i.e., hard disk, CD-ROM, etc.). You may also use a copy of the Software on a home or portable computer, provided only one copy of the Software is in use at a time. You may use an evaluation copy of the Software for only thirty (30) days in order to determine whether to purchase the Software.

4. MULTI-COMPUTER LICENSES.  If this is a multi-computer license, you may make, install, and use additional copies of this Software up to the number of copies authorized in your registration documentation. Unless you have a terminal server license agreement signed by VanDyke Software, Inc., use of this Software is not allowed in environments in which the Software is located on a central server and is accessed by multiple computers. If this is an educational license, use is restricted to non-commercial use by students, faculty, and staff using campus facilities, and to off-campus non-commercial use by students, faculty, and staff primarily for school business.

5. LIMITED WARRANTY. THE SOFTWARE IS PROVIDED AS IS AND VANDYKE SOFTWARE DISCLAIMS ALL WARRANTIES RELATING TO THIS SOFTWARE, WHETHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE. 

6. LIMITATION ON DAMAGES. NEITHER VANDYKE SOFTWARE NOR ANYONE INVOLVED IN THE CREATION, PRODUCTION, OR DELIVERY OF THIS SOFTWARE SHALL BE LIABLE FOR ANY INDIRECT, CONSEQUENTIAL, OR INCIDENTAL DAMAGES ARISING OUT OF THE USE OR INABILITY TO USE SUCH SOFTWARE EVEN IF VANDYKE SOFTWARE HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES OR CLAIMS. IN NO EVENT SHALL VANDYKE SOFTWARE'S LIABILITY FOR ANY DAMAGES EXCEED THE PRICE PAID FOR THE LICENSE TO USE THE SOFTWARE, REGARDLESS OF THE FORM OF CLAIM. THE PERSON USING THE SOFTWARE BEARS ALL RISK AS TO THE QUALITY AND PERFORMANCE OF THE SOFTWARE.

7. TERMINATION. This Agreement terminates on the date of the first occurrence of either of the following events: (1) The expiration of one (1) month from written notice of termination from Customer to VanDyke Software, Inc.; or (2) One party materially breaches any terms of this Agreement or any terms of any other agreement between Customer and VanDyke Software, Inc., that are either uncorrectable or that the breaching party fails to correct within one (1) month after written notification by the other party.

8. GOVERNING LAW. The agreement shall be governed by the laws of the State of New Mexico. Any action or proceeding brought by either party against the other arising out of or related to this agreement shall be brought only in a state or federal court of competent jurisdiction located in Bernalillo County, New Mexico. The parties hereby consent to the personal jurisdiction of such courts.

9. U.S. GOVERNMENT RESTRICTED RIGHTS. This Software is provided with RESTRICTED RIGHTS. Use, duplication, or disclosure by the Government is subject to restrictions as set forth in subparagraph (a) of the Rights in Commercial Computer Software clause at DFARS 227.7202-3 or subparagraphs (c)(1) and (2) of the Commercial Computer Software -- Restricted Rights clause at 48 CFR 52.227-19, as applicable. Manufacturer is:

VanDyke Software, Inc.
4848 Tramway Ridge Dr. NE, Suite 101
Albuquerque, NM 87111 USA
E-mail: <EMAIL>
