2024-12-22T18:47:20.133Z In(05) vmx Log for VMware Workstation pid=4020 version=17.0.0 build=build-20800274 option=Release
2024-12-22T18:47:20.133Z In(05) vmx The host is x86_64.
2024-12-22T18:47:20.133Z In(05) vmx Host codepage=windows-1252 encoding=windows-1252
2024-12-22T18:47:20.133Z In(05) vmx Host is Windows 10 Pro, 64-bit (Build 19045.5247)
2024-12-22T18:47:20.133Z In(05) vmx Host offset from UTC is -07:00.
2024-12-22T18:47:20.100Z In(05) vmx VTHREAD 4180 "vmx"
2024-12-22T18:47:20.101Z In(05) vmx LOCALE windows-1252 -> NULL User=409 System=409
2024-12-22T18:47:20.101Z In(05) vmx Msg_SetLocaleEx: HostLocale=windows-1252 UserLocale=NULL
2024-12-22T18:47:20.107Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-12-22T18:47:20.107Z In(05) vmx Msg_Reset:
2024-12-22T18:47:20.107Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-12-22T18:47:20.107Z In(05) vmx ----------------------------------------
2024-12-22T18:47:20.107Z In(05) vmx ConfigDB: Failed to load C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2024-12-22T18:47:20.108Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("H:\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmpl", ...) failed, error: 2
2024-12-22T18:47:20.109Z In(05) vmx OBJLIB-LIB: Objlib initialized.
2024-12-22T18:47:20.110Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-12-22T18:47:20.110Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2024-12-22T18:47:20.110Z In(05) vmx PREF Optional preferences file not found at C:\Users\<USER>\AppData\Roaming\VMware\config.ini. Using default values.
2024-12-22T18:47:20.130Z In(05) vmx lib/ssl: OpenSSL using RAND_OpenSSL for RAND
2024-12-22T18:47:20.130Z In(05) vmx lib/ssl: protocol list tls1.2
2024-12-22T18:47:20.130Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2024-12-22T18:47:20.130Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2024-12-22T18:47:20.130Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2024-12-22T18:47:20.137Z In(05) vmx Hostname=DESKTOP-RKK1A31
2024-12-22T18:47:20.141Z In(05) vmx IP=fe80::3651:d18c:3e65:2ac3%21
2024-12-22T18:47:20.141Z In(05) vmx IP=2001:ee0:4f81:4170:b4b8:be6:67e7:ff60
2024-12-22T18:47:20.141Z In(05) vmx IP=2001:ee0:4f81:4170:e700:a556:87ad:cc
2024-12-22T18:47:20.141Z In(05) vmx IP=fe80::2c0f:3188:3871:9b86%14
2024-12-22T18:47:20.141Z In(05) vmx IP=fe80::214:bf14:b0c:223e%13
2024-12-22T18:47:20.141Z In(05) vmx IP=fe80::ce36:2e66:57c7:74c0%9
2024-12-22T18:47:20.141Z In(05) vmx IP=fe80::a7ac:a1d0:d931:7550%12
2024-12-22T18:47:20.141Z In(05) vmx IP=fe80::c21f:7b2f:6849:89ed%6
2024-12-22T18:47:20.141Z In(05) vmx IP=fe80::478:f1f:f156:7bb3%15
2024-12-22T18:47:20.141Z In(05) vmx IP=************
2024-12-22T18:47:20.141Z In(05) vmx IP=***********
2024-12-22T18:47:20.141Z In(05) vmx IP=*************
2024-12-22T18:47:20.141Z In(05) vmx IP=************
2024-12-22T18:47:20.141Z In(05) vmx IP=***********
2024-12-22T18:47:20.141Z In(05) vmx IP=*************
2024-12-22T18:47:20.141Z In(05) vmx IP=2001:0:2851:fcb0:478:f1f:f156:7bb3
2024-12-22T18:47:20.141Z In(05) vmx IP=fdfd::1a58:519c
2024-12-22T18:47:20.170Z In(05) vmx System uptime 195846988 us
2024-12-22T18:47:20.170Z In(05) vmx Command line: "C:\Program Files (x86)\VMware\VMware Workstation\x64\vmware-vmx.exe" "-s" "vmx.stdio.keep=TRUE" "-#" "product=1;name=VMware Workstation;version=17.0.0;buildnumber=20800274;licensename=VMware Workstation;licenseversion=17.0;" "-@" "pipe=\\.\pipe\vmx30dca198da6dbc90;msgs=ui" "H:\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmx"
2024-12-22T18:47:20.170Z In(05) vmx Msg_SetLocaleEx: HostLocale=windows-1252 UserLocale=NULL
2024-12-22T18:47:20.200Z In(05) vmx WQPoolAllocPoll : pollIx = 1, signalHandle = 816
2024-12-22T18:47:20.200Z In(05) vmx WQPoolAllocPoll : pollIx = 2, signalHandle = 828
2024-12-22T18:47:20.203Z In(05) vmx VigorTransport listening on fd 748
2024-12-22T18:47:20.203Z In(05) vmx Vigor_Init 1
2024-12-22T18:47:20.203Z In(05) vmx Connecting 'ui' to pipe '\\.\pipe\vmx30dca198da6dbc90' with user '(null)'
2024-12-22T18:47:20.203Z In(05) vmx VMXVmdb: Local connection timeout: 60000 ms.
2024-12-22T18:47:20.280Z In(05) vmx VmdbAddConnection: cnxPath=/db/connection/#1/, cnxIx=1
2024-12-22T18:47:20.282Z In(05) vmx Vix: [mainDispatch.c:488]: VMAutomation: Initializing VMAutomation.
2024-12-22T18:47:20.283Z In(05) vmx Vix: [mainDispatch.c:740]: VMAutomationOpenListenerSocket() listening
2024-12-22T18:47:20.288Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2024-12-22T18:47:20.288Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2024-12-22T18:47:20.288Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-12-22T18:47:20.288Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1877, success=1 additionalError=0
2024-12-22T18:47:20.288Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=3, newAppState=1881, success=1 additionalError=0
2024-12-22T18:47:20.308Z In(05) vmx IOPL_VBSRunning: VBS is set to 0
2024-12-22T18:47:20.309Z In(05) vmx Locked limit delta -2560 pages
2024-12-22T18:47:20.310Z In(05) vmx Locked limit delta 2560 pages
2024-12-22T18:47:20.311Z In(05) vmx Locked limit delta -2560 pages
2024-12-22T18:47:20.311Z In(05) vmx Locked limit delta 2560 pages
2024-12-22T18:47:20.311Z In(05) vmx Locked limit delta -2560 pages
2024-12-22T18:47:20.312Z In(05) vmx Locked limit delta 2560 pages
2024-12-22T18:47:20.312Z In(05) vmx Locked limit delta -2560 pages
2024-12-22T18:47:20.313Z In(05) vmx Locked limit delta 2560 pages
2024-12-22T18:47:20.313Z In(05) vmx Locked limit delta -2560 pages
2024-12-22T18:47:20.313Z In(05) vmx Locked limit delta 2560 pages
2024-12-22T18:47:20.313Z In(05) vmx VerificationOfHostParameters status 0
2024-12-22T18:47:20.314Z In(05) vmx FeatureCompat: No EVC masks.
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID vendor: GenuineIntel
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID family: 0x6 model: 0x97 stepping: 0x5
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID codename: Alder Lake-S
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID name: 12th Gen Intel(R) Core(TM) i5-12400F
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000000, 0: 0x00000020 0x756e6547 0x6c65746e 0x49656e69
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000001, 0: 0x00090675 0x00100800 0x7ffafbbf 0xbfebfbff
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000002, 0: 0x00feff01 0x000000f0 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000004, 0: 0x1c004121 0x02c0003f 0x0000003f 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000004, 1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000004, 2: 0x1c004143 0x0240003f 0x000007ff 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000004, 3: 0x1c03c163 0x02c0003f 0x00005fff 0x00000004
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000004, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x10101020
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000006, 0: 0x00df8ff7 0x00000002 0x00000401 0x00000003
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000007, 0: 0x00000001 0x239ca7eb 0x98c007ac 0xfc184410
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000007, 1: 0x00401c10 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000008, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000a, 0: 0x08300805 0x00000000 0x0000000f 0x00008604
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000b, 0: 0x00000001 0x00000002 0x00000100 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000b, 1: 0x00000004 0x0000000c 0x00000201 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000b, 2: 0x00000000 0x00000000 0x00000002 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, 0: 0x00000207 0x00000340 0x00000a88 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, 1: 0x0000000f 0x000003d0 0x00019900 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, 2: 0x00000100 0x00000240 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, 5: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, 6: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, 7: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, 8: 0x00000080 0x00000000 0x00000001 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, 9: 0x00000008 0x00000a80 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, a: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, b: 0x00000010 0x00000000 0x00000001 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, c: 0x00000018 0x00000000 0x00000001 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, d: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, e: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, f: 0x00000328 0x00000000 0x00000001 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000d, 10: 0x00000008 0x00000000 0x00000001 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000000f, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000010, 0: 0x00000000 0x00000004 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000010, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000010, 2: 0x00000009 0x00000000 0x00000004 0x00000007
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000010, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000012, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000012, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000012, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000012, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000014, 0: 0x00000001 0x0000005f 0x00000007 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000014, 1: 0x02490002 0x003f003f 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000015, 0: 0x00000002 0x00000082 0x0249f000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000016, 0: 0x000009c4 0x00001130 0x00000064 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000017, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000018, 0: 0x00000008 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000018, 1: 0x00000000 0x00080001 0x00000020 0x00004022
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000018, 2: 0x00000000 0x00080006 0x00000004 0x00004022
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000018, 3: 0x00000000 0x0010000f 0x00000001 0x00004125
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000018, 4: 0x00000000 0x00040001 0x00000010 0x00004024
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000018, 5: 0x00000000 0x00040006 0x00000008 0x00004024
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000018, 6: 0x00000000 0x00080008 0x00000001 0x00004124
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000018, 7: 0x00000000 0x00080007 0x00000080 0x00004043
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000018, 8: 0x00000000 0x00080009 0x00000080 0x00004043
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000019, 0: 0x00000007 0x00000014 0x00000003 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000001a, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000001b, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000001c, 0: 0x4000000b 0x00000007 0x00000007 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000001d, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000001e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000001f, 0: 0x00000001 0x00000002 0x00000100 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000001f, 1: 0x00000004 0x0000000c 0x00000201 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 0000001f, 2: 0x00000000 0x00000000 0x00000002 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 00000020, 0: 0x00000000 0x00000001 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 80000000, 0: 0x80000008 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 80000001, 0: 0x00000000 0x00000000 0x00000121 0x2c100800
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 80000002, 0: 0x68743231 0x6e654720 0x746e4920 0x52286c65
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 80000003, 0: 0x6f432029 0x54286572 0x6920294d 0x32312d35
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 80000004, 0: 0x46303034 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 80000005, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 80000006, 0: 0x00000000 0x00000000 0x05007040 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x00000100
2024-12-22T18:47:20.318Z In(05) vmx hostCPUID level 80000008, 0: 0x00003027 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.318Z In(05) vmx CPUID differences from hostCPUID.
2024-12-22T18:47:20.318Z In(05) vmx CPUID[2] level 00000006, 0: 0x00df8ff7 0x00000002 0x00000401 0x00010003
2024-12-22T18:47:20.318Z In(05) vmx CPUID[3] level 00000006, 0: 0x00df8ff7 0x00000002 0x00000401 0x00010003
2024-12-22T18:47:20.318Z In(05) vmx CPUID[4] level 00000006, 0: 0x00df8ff7 0x00000002 0x00000401 0x00020003
2024-12-22T18:47:20.318Z In(05) vmx CPUID[5] level 00000006, 0: 0x00df8ff7 0x00000002 0x00000401 0x00020003
2024-12-22T18:47:20.318Z In(05) vmx CPUID[6] level 00000006, 0: 0x00df8ff7 0x00000002 0x00000401 0x00030003
2024-12-22T18:47:20.318Z In(05) vmx CPUID[7] level 00000006, 0: 0x00df8ff7 0x00000002 0x00000401 0x00030003
2024-12-22T18:47:20.318Z In(05) vmx CPUID[8] level 00000006, 0: 0x00df8ff7 0x00000002 0x00000401 0x00040003
2024-12-22T18:47:20.318Z In(05) vmx CPUID[9] level 00000006, 0: 0x00df8ff7 0x00000002 0x00000401 0x00040003
2024-12-22T18:47:20.318Z In(05) vmx CPUID[10] level 00000006, 0: 0x00df8ff7 0x00000002 0x00000401 0x00050003
2024-12-22T18:47:20.318Z In(05) vmx CPUID[11] level 00000006, 0: 0x00df8ff7 0x00000002 0x00000401 0x00050003
2024-12-22T18:47:20.318Z In(05) vmx Physical APIC IDs: 0-11
2024-12-22T18:47:20.318Z In(05) vmx Physical X2APIC IDs: 0-11
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR       0x3a =                0x5
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x480 =  0x3da050000000013
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x481 =       0xff00000016
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x482 = 0xfffbfffe0401e172
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x483 = 0xf77fffff00036dff
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x484 =   0x76ffff000011ff
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x485 =         0x7004c1e7
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x486 =         0x80000021
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x487 =         0xffffffff
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x488 =             0x2000
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x489 =          0x1ff2fff
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x48a =               0x2e
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x48b =  0x75f7fff00000000
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x48c =      0xf0106f34141
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x48d =       0xff00000016
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x48e = 0xfffbfffe04006172
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x48f = 0xf77fffff00036dfb
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x490 =   0x76ffff000011fb
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x491 =                0x1
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x492 =                0x1
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR 0xc0010114 =                  0
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR       0xce =         0x80000000
2024-12-22T18:47:20.318Z In(05) vmx Common: MSR      0x10a =              0xd6b
2024-12-22T18:47:20.319Z In(05) vmx Common: MSR      0x122 =                  0
2024-12-22T18:47:20.319Z In(05) vmx VMMon_GetkHzEstimate: Calculated 2496002 kHz
2024-12-22T18:47:20.319Z In(05) vmx TSC Hz estimates: vmmon 2496002000, remembered 0, osReported 2500000000. Using 2496002000 Hz.
2024-12-22T18:47:20.319Z In(05) vmx TSC first measured delta 326
2024-12-22T18:47:20.319Z In(05) vmx TSC min delta 312
2024-12-22T18:47:20.319Z In(05) vmx PTSC: RefClockToPTSC 0 @ 10000000Hz -> 0 @ 2496002000Hz
2024-12-22T18:47:20.319Z In(05) vmx PTSC: RefClockToPTSC ((x * 4187596469) >> 24) + -489585722915
2024-12-22T18:47:20.319Z In(05) vmx PTSC: tscOffset -516662486679
2024-12-22T18:47:20.319Z In(05) vmx PTSC: using TSC
2024-12-22T18:47:20.319Z In(05) vmx PTSC: hardware TSCs are synchronized.
2024-12-22T18:47:20.319Z In(05) vmx PTSC: hardware TSCs may have been adjusted by the host.
2024-12-22T18:47:20.319Z In(05) vmx PTSC: current PTSC=138039
2024-12-22T18:47:20.324Z In(05) vmx WQPoolAllocPoll : pollIx = 3, signalHandle = 1036
2024-12-22T18:47:20.353Z In(05) vmx ConfigCheck: No rules file found. Checks are disabled.
2024-12-22T18:47:20.353Z In(05) vmx changing directory to H:\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\.
2024-12-22T18:47:20.353Z In(05) vmx Config file: H:\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmx
2024-12-22T18:47:20.353Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-12-22T18:47:20.353Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1878, success=1 additionalError=0
2024-12-22T18:47:20.544Z Wa(03) vmx PowerOn
2024-12-22T18:47:20.544Z In(05) vmx VMX_PowerOn: VMX build 20800274, UI build 20800274
2024-12-22T18:47:20.544Z In(05) vmx HostWin32: WIN32 NUMA node 0, CPU mask 0x0000000000000fff
2024-12-22T18:47:20.547Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1871, success=1 additionalError=0
2024-12-22T18:47:20.597Z In(05) vmx VMXSTATS: Successfully created statsfile: LouLx-Game.scoreboard
2024-12-22T18:47:20.597Z In(05) vmx VMXSTATS: Update Product Information: VMware Workstation	17.0.0	build-20800274	Release  TotalBlockSize: 64
2024-12-22T18:47:20.598Z In(05) vmx HOST Windows version 10.0, build 19045, platform 2, ""
2024-12-22T18:47:20.598Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini
2024-12-22T18:47:20.598Z In(05) vmx DICT          printers.enabled = "FALSE"
2024-12-22T18:47:20.598Z In(05) vmx DICT --- NON PERSISTENT (null)
2024-12-22T18:47:20.598Z In(05) vmx DICT --- USER PREFERENCES C:\Users\<USER>\AppData\Roaming\VMware\preferences.ini
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.updatesVersionIgnore.numItems = "6"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.updatesVersionIgnore0.key = <not printed>
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.updatesVersionIgnore0.value = "35f211f8-915f-4000-8d14-a379e4990924"
2024-12-22T18:47:20.598Z In(05) vmx DICT   pref.lastUpdateCheckSec = "1734893234"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.enabled = "FALSE"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.count = "0"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window.count = "1"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.tab.count = "1"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.sidebar = "FALSE"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.sidebar.width = "200"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.statusBar = "TRUE"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.tabs = "TRUE"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar = "FALSE"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.size = "100"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.view = "opened-vms"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.placement.left = "36"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.placement.top = "116"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.placement.right = "1958"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.placement.bottom = "1157"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.maximized = "TRUE"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.tab0.cnxType = "vmdb"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.tab1.cnxType = "vmdb"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.tab2.cnxType = "vmdb"
2024-12-22T18:47:20.598Z In(05) vmx DICT             hints.hideAll = "FALSE"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.tab3.cnxType = "vmdb"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.updatesVersionIgnore1.key = <not printed>
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.updatesVersionIgnore1.value = "424c449b-b863-4e9e-b232-c97afcd13923"
2024-12-22T18:47:20.598Z In(05) vmx DICT hint.cui.toolsInfoBar.suppressible = "FALSE"
2024-12-22T18:47:20.598Z In(05) vmx DICT  hint.disk.fragmented.low = "FALSE"
2024-12-22T18:47:20.598Z In(05) vmx DICT           hint.vmui.reset = "FALSE"
2024-12-22T18:47:20.598Z In(05) vmx DICT  hint.vmui.showNewUSBDevs = "FALSE"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.updatesVersionIgnore2.key = <not printed>
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.updatesVersionIgnore2.value = "6445c082-fad2-40d7-a85f-4db24d69fee2"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.updatesVersionIgnore3.key = <not printed>
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.updatesVersionIgnore3.value = "e9e56989-87e8-402b-826d-124fec0c3671"
2024-12-22T18:47:20.598Z In(05) vmx DICT         vmWizard.guestKey = "windows9-64"
2024-12-22T18:47:20.598Z In(05) vmx DICT vmWizard.installMediaType = "iso"
2024-12-22T18:47:20.598Z In(05) vmx DICT vmWizard.isoLocationMRU.count = "4"
2024-12-22T18:47:20.598Z In(05) vmx DICT vmWizard.isoLocationMRU0.location = "G:\O dia D\!Backup\en-us_windows_10_consumer_editions_version_21h2_x64_dvd_6cfdb144.iso"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.updatesVersionIgnore4.key = <not printed>
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.updatesVersionIgnore4.value = "ff1afc25-00fa-42c8-96a5-a1fff69c7d48"
2024-12-22T18:47:20.598Z In(05) vmx DICT vmWizard.isoLocationMRU1.location = "C:\Users\<USER>\Downloads\CentOS-7-x86_64-LiveKDE-1511.iso"
2024-12-22T18:47:20.598Z In(05) vmx DICT vmWizard.isoLocationMRU2.location = "C:\Users\<USER>\Downloads\CentOS-7-x86_64-Minimal-1511.iso"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.tab0.dest = ""
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.tab0.file = "C:\Users\<USER>\Documents\Virtual Machines\LouLx-Game\LouLx-Game.vmx"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.tab0.type = "vm"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.ws.session.window0.tab0.focused = "TRUE"
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.updatesVersionIgnore5.key = <not printed>
2024-12-22T18:47:20.598Z In(05) vmx DICT pref.updatesVersionIgnore5.value = "bb61d294-c7fd-4b93-bdb3-48a9b5b74f44"
2024-12-22T18:47:20.598Z In(05) vmx DICT vmWizard.isoLocationMRU3.location = "H:\VM\ubuntu-22.04.5-live-server-amd64.iso"
2024-12-22T18:47:20.598Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2024-12-22T18:47:20.598Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2024-12-22T18:47:20.598Z In(05) vmx DICT         authd.client.port = "902"
2024-12-22T18:47:20.598Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-12-22T18:47:20.598Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2024-12-22T18:47:20.598Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "21151"
2024-12-22T18:47:20.598Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-12-22T18:47:20.598Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2024-12-22T18:47:20.598Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "21151"
2024-12-22T18:47:20.598Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2024-12-22T18:47:20.598Z In(05) vmx DICT         authd.client.port = "902"
2024-12-22T18:47:20.598Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-12-22T18:47:20.598Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2024-12-22T18:47:20.598Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "21151"
2024-12-22T18:47:20.599Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-12-22T18:47:20.599Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2024-12-22T18:47:20.599Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "21151"
2024-12-22T18:47:20.599Z In(05) vmx DICT --- NONPERSISTENT
2024-12-22T18:47:20.599Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT             gui.available = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT --- COMMAND LINE
2024-12-22T18:47:20.599Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT             gui.available = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT --- RECORDING
2024-12-22T18:47:20.599Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT             gui.available = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT --- CONFIGURATION H:\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmx 
2024-12-22T18:47:20.599Z In(05) vmx DICT            config.version = "8"
2024-12-22T18:47:20.599Z In(05) vmx DICT         virtualHW.version = "16"
2024-12-22T18:47:20.599Z In(05) vmx DICT        pciBridge0.present = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT        pciBridge4.present = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT     pciBridge4.virtualDev = "pcieRootPort"
2024-12-22T18:47:20.599Z In(05) vmx DICT      pciBridge4.functions = "8"
2024-12-22T18:47:20.599Z In(05) vmx DICT        pciBridge5.present = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT     pciBridge5.virtualDev = "pcieRootPort"
2024-12-22T18:47:20.599Z In(05) vmx DICT      pciBridge5.functions = "8"
2024-12-22T18:47:20.599Z In(05) vmx DICT        pciBridge6.present = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT     pciBridge6.virtualDev = "pcieRootPort"
2024-12-22T18:47:20.599Z In(05) vmx DICT      pciBridge6.functions = "8"
2024-12-22T18:47:20.599Z In(05) vmx DICT        pciBridge7.present = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT     pciBridge7.virtualDev = "pcieRootPort"
2024-12-22T18:47:20.599Z In(05) vmx DICT      pciBridge7.functions = "8"
2024-12-22T18:47:20.599Z In(05) vmx DICT             vmci0.present = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT             hpet0.present = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT                     nvram = "LouLx-Game.nvram"
2024-12-22T18:47:20.599Z In(05) vmx DICT virtualHW.productCompatibility = "hosted"
2024-12-22T18:47:20.599Z In(05) vmx DICT        powerType.powerOff = "soft"
2024-12-22T18:47:20.599Z In(05) vmx DICT         powerType.powerOn = "soft"
2024-12-22T18:47:20.599Z In(05) vmx DICT         powerType.suspend = "soft"
2024-12-22T18:47:20.599Z In(05) vmx DICT           powerType.reset = "soft"
2024-12-22T18:47:20.599Z In(05) vmx DICT               displayName = "Đại Chiến Quốc Mobile LouLx"
2024-12-22T18:47:20.599Z In(05) vmx DICT usb.vbluetooth.startConnected = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT                   guestOS = "centos7-64"
2024-12-22T18:47:20.599Z In(05) vmx DICT            tools.syncTime = "FALSE"
2024-12-22T18:47:20.599Z In(05) vmx DICT          sound.autoDetect = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT            sound.fileName = "-1"
2024-12-22T18:47:20.599Z In(05) vmx DICT                  numvcpus = "2"
2024-12-22T18:47:20.599Z In(05) vmx DICT      cpuid.coresPerSocket = "2"
2024-12-22T18:47:20.599Z In(05) vmx DICT               vcpu.hotadd = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT                   memsize = "4096"
2024-12-22T18:47:20.599Z In(05) vmx DICT                mem.hotadd = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT          scsi0.virtualDev = "lsilogic"
2024-12-22T18:47:20.599Z In(05) vmx DICT             scsi0.present = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT          scsi0:0.fileName = "LouLx-Game.vmdk"
2024-12-22T18:47:20.599Z In(05) vmx DICT           scsi0:0.present = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT         ide1:0.deviceType = "cdrom-image"
2024-12-22T18:47:20.599Z In(05) vmx DICT           ide1:0.fileName = "E:\VM\CentOS-7-x86_64-Minimal-2009.iso"
2024-12-22T18:47:20.599Z In(05) vmx DICT  ethernet0.connectionType = "nat"
2024-12-22T18:47:20.599Z In(05) vmx DICT     ethernet0.addressType = "generated"
2024-12-22T18:47:20.599Z In(05) vmx DICT      ethernet0.virtualDev = "e1000"
2024-12-22T18:47:20.599Z In(05) vmx DICT          serial0.fileType = "thinprint"
2024-12-22T18:47:20.599Z In(05) vmx DICT          serial0.fileName = "thinprint"
2024-12-22T18:47:20.599Z In(05) vmx DICT         ethernet0.present = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT        extendedConfigFile = "LouLx-Game.vmxf"
2024-12-22T18:47:20.599Z In(05) vmx DICT           floppy0.present = "FALSE"
2024-12-22T18:47:20.599Z In(05) vmx DICT                 uuid.bios = "56 4d 08 42 91 55 78 d0-35 2b 25 5c bc d6 20 c7"
2024-12-22T18:47:20.599Z In(05) vmx DICT             uuid.location = "56 4d 37 ba e7 c1 1d c8-84 ad b8 63 fc f3 cc dd"
2024-12-22T18:47:20.599Z In(05) vmx DICT              scsi0:0.redo = ""
2024-12-22T18:47:20.599Z In(05) vmx DICT  pciBridge0.pciSlotNumber = "17"
2024-12-22T18:47:20.599Z In(05) vmx DICT  pciBridge4.pciSlotNumber = "21"
2024-12-22T18:47:20.599Z In(05) vmx DICT  pciBridge5.pciSlotNumber = "22"
2024-12-22T18:47:20.599Z In(05) vmx DICT  pciBridge6.pciSlotNumber = "23"
2024-12-22T18:47:20.599Z In(05) vmx DICT  pciBridge7.pciSlotNumber = "24"
2024-12-22T18:47:20.599Z In(05) vmx DICT       scsi0.pciSlotNumber = "16"
2024-12-22T18:47:20.599Z In(05) vmx DICT         usb.pciSlotNumber = "-1"
2024-12-22T18:47:20.599Z In(05) vmx DICT   ethernet0.pciSlotNumber = "33"
2024-12-22T18:47:20.599Z In(05) vmx DICT       sound.pciSlotNumber = "-1"
2024-12-22T18:47:20.599Z In(05) vmx DICT        ehci.pciSlotNumber = "-1"
2024-12-22T18:47:20.599Z In(05) vmx DICT             svga.vramSize = "268435456"
2024-12-22T18:47:20.599Z In(05) vmx DICT  vmotion.checkpointFBSize = "4194304"
2024-12-22T18:47:20.599Z In(05) vmx DICT vmotion.checkpointSVGAPrimarySize = "268435456"
2024-12-22T18:47:20.599Z In(05) vmx DICT ethernet0.generatedAddress = "00:0c:29:d6:20:c7"
2024-12-22T18:47:20.599Z In(05) vmx DICT ethernet0.generatedAddressOffset = "0"
2024-12-22T18:47:20.599Z In(05) vmx DICT                  vmci0.id = "-1126817593"
2024-12-22T18:47:20.599Z In(05) vmx DICT    monitor.phys_bits_used = "43"
2024-12-22T18:47:20.599Z In(05) vmx DICT             cleanShutdown = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT              softPowerOff = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT             usb:0.present = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT          usb:0.deviceType = "hid"
2024-12-22T18:47:20.599Z In(05) vmx DICT                usb:0.port = "0"
2024-12-22T18:47:20.599Z In(05) vmx DICT              usb:0.parent = "-1"
2024-12-22T18:47:20.599Z In(05) vmx DICT               usb:1.speed = "2"
2024-12-22T18:47:20.599Z In(05) vmx DICT             usb:1.present = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT          usb:1.deviceType = "hub"
2024-12-22T18:47:20.599Z In(05) vmx DICT                usb:1.port = "1"
2024-12-22T18:47:20.599Z In(05) vmx DICT              usb:1.parent = "-1"
2024-12-22T18:47:20.599Z In(05) vmx DICT svga.guestBackedPrimaryAware = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT       tools.remindInstall = "TRUE"
2024-12-22T18:47:20.599Z In(05) vmx DICT                annotation = <not printed>
2024-12-22T18:47:20.599Z In(05) vmx DICT         vmxstats.filename = "LouLx-Game.scoreboard"
2024-12-22T18:47:20.599Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini 
2024-12-22T18:47:20.599Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2024-12-22T18:47:20.599Z In(05) vmx DICT         authd.client.port = "902"
2024-12-22T18:47:20.599Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-12-22T18:47:20.599Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2024-12-22T18:47:20.599Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "21151"
2024-12-22T18:47:20.599Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-12-22T18:47:20.599Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2024-12-22T18:47:20.599Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "21151"
2024-12-22T18:47:20.599Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2024-12-22T18:47:20.599Z In(05) vmx DICT         authd.client.port = "902"
2024-12-22T18:47:20.599Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2024-12-22T18:47:20.599Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2024-12-22T18:47:20.599Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "21151"
2024-12-22T18:47:20.599Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2024-12-22T18:47:20.599Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2024-12-22T18:47:20.599Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "21151"
2024-12-22T18:47:20.599Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini 
2024-12-22T18:47:20.599Z In(05) vmx DICT          printers.enabled = "FALSE"
2024-12-22T18:47:20.599Z In(05) vmx Powering on guestOS 'centos7-64' using the configuration for 'centos7-64'.
2024-12-22T18:47:20.605Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-12-22T18:47:20.605Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-12-22T18:47:20.605Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2024-12-22T18:47:20.605Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'centos7-64' guest.
2024-12-22T18:47:20.606Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-12-22T18:47:20.607Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2024-12-22T18:47:20.609Z In(05) vmx Monitor Mode: CPL0
2024-12-22T18:47:20.622Z In(05) vmx MemSched_EarlyPowerOn: balloon minGuestSize 104857 (80% of min required size 131072)
2024-12-22T18:47:20.623Z In(05) vmx OvhdMem_PowerOn: initial admission: paged   574477 nonpaged     5630 anonymous     8313
2024-12-22T18:47:20.623Z In(05) vmx VMMEM: Initial Reservation: 2298MB (MainMem=4096MB)
2024-12-22T18:47:20.623Z In(05) vmx numa: Hot-add is enabled and vNUMA hot-add is disabled, forcing UMA.
2024-12-22T18:47:20.623Z In(05) vmx llc: maximum vcpus per LLC: 1
2024-12-22T18:47:20.623Z In(05) vmx llc: vLLC size: 2
2024-12-22T18:47:20.627Z In(05) vmx MemSched: reserved mem (in MB) min 128 max 57221 recommended 57221
2024-12-22T18:47:20.627Z In(05) vmx MemSched: pg 574477 np 5630 anon 8313 mem 1048576
2024-12-22T18:47:20.635Z In(05) vmx MemSched: numvm 1 locked pages: num 0 max 14640384
2024-12-22T18:47:20.635Z In(05) vmx MemSched: locked Page Limit: host 16185620 config 14648576
2024-12-22T18:47:20.635Z In(05) vmx MemSched: minmempct 50 minalloc 0 admitted 1
2024-12-22T18:47:20.636Z In(05) PowerNotifyThread VTHREAD 4336 "PowerNotifyThread"
2024-12-22T18:47:20.636Z In(05) PowerNotifyThread PowerNotify thread is alive.
2024-12-22T18:47:20.636Z In(05) vmx VMXSTATS: Registering 1 stats: vmx.logDropChars
2024-12-22T18:47:20.636Z In(05) vmx VMXSTATS: Registering 2 stats: vmx.logBytesLogged
2024-12-22T18:47:20.636Z In(05) vmx VMXSTATS: Registering 3 stats: vmx.numTimesLogDrop
2024-12-22T18:47:20.637Z In(05) vmx LICENSE using: 'HKEY_LOCAL_MACHINE\SOFTWARE\VMware, Inc.\VMware Workstation\Dormant\License.ws.17.0.e2.202208' 
2024-12-22T18:47:20.637Z In(05) vthread-6612 VTHREAD 6612 "vthread-6612"
2024-12-22T18:47:20.637Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("H:\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmpl", ...) failed, error: 2
2024-12-22T18:47:20.637Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2024-12-22T18:47:20.637Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("H:\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmpl", ...) failed, error: 2
2024-12-22T18:47:20.637Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2024-12-22T18:47:20.638Z In(05) vmx Host PA size: 39 bits. Guest PA size: 43 bits.
2024-12-22T18:47:20.640Z In(05) vmx ToolsISO: Refreshing imageName for 'centos7-64' (refreshCount=1, lastCount=1).
2024-12-22T18:47:20.640Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-12-22T18:47:20.640Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-12-22T18:47:20.641Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2024-12-22T18:47:20.641Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'centos7-64' guest.
2024-12-22T18:47:20.641Z In(05) deviceThread VTHREAD 4416 "deviceThread"
2024-12-22T18:47:20.641Z In(05) deviceThread Device thread is alive
2024-12-22T18:47:20.642Z In(05) vmx Host VT-x Capabilities:
2024-12-22T18:47:20.642Z In(05) vmx Basic VMX Information (0x03da050000000013)
2024-12-22T18:47:20.642Z In(05) vmx   VMCS revision ID                          19
2024-12-22T18:47:20.642Z In(05) vmx   VMCS region length                      1280
2024-12-22T18:47:20.642Z In(05) vmx   VMX physical-address width           natural
2024-12-22T18:47:20.642Z In(05) vmx   SMM dual-monitor mode                    yes
2024-12-22T18:47:20.642Z In(05) vmx   VMCS memory type                          WB
2024-12-22T18:47:20.642Z In(05) vmx   Advanced INS/OUTS info                   yes
2024-12-22T18:47:20.642Z In(05) vmx   True VMX MSRs                            yes
2024-12-22T18:47:20.642Z In(05) vmx   Exception Injection ignores error code   yes
2024-12-22T18:47:20.642Z In(05) vmx True Pin-Based VM-Execution Controls (0x000000ff00000016)
2024-12-22T18:47:20.642Z In(05) vmx   External-interrupt exiting               {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   NMI exiting                              {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Virtual NMIs                             {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Activate VMX-preemption timer            {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Process posted interrupts                {0,1}
2024-12-22T18:47:20.642Z In(05) vmx True Primary Processor-Based VM-Execution Controls (0xfffbfffe04006172)
2024-12-22T18:47:20.642Z In(05) vmx   Interrupt-window exiting                 {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Use TSC offsetting                       {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   HLT exiting                              {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   INVLPG exiting                           {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   MWAIT exiting                            {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   RDPMC exiting                            {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   RDTSC exiting                            {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   CR3-load exiting                         {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   CR3-store exiting                        {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Activate tertiary controls               {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   CR8-load exiting                         {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   CR8-store exiting                        {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Use TPR shadow                           {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   NMI-window exiting                       {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   MOV-DR exiting                           {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Unconditional I/O exiting                {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Use I/O bitmaps                          {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Monitor trap flag                        {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Use MSR bitmaps                          {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   MONITOR exiting                          {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   PAUSE exiting                            {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Activate secondary controls              {0,1}
2024-12-22T18:47:20.642Z In(05) vmx Secondary Processor-Based VM-Execution Controls (0x075f7fff00000000)
2024-12-22T18:47:20.642Z In(05) vmx   Virtualize APIC accesses                 {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Enable EPT                               {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Descriptor-table exiting                 {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Enable RDTSCP                            {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Virtualize x2APIC mode                   {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Enable VPID                              {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   WBINVD exiting                           {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Unrestricted guest                       {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   APIC-register virtualization             {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Virtual-interrupt delivery               {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   PAUSE-loop exiting                       {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   RDRAND exiting                           {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Enable INVPCID                           {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Enable VM Functions                      {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Use VMCS shadowing                       {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   ENCLS exiting                            { 0 }
2024-12-22T18:47:20.642Z In(05) vmx   RDSEED exiting                           {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Enable PML                               {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   EPT-violation #VE                        {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Conceal VMX from PT                      {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Enable XSAVES/XRSTORS                    {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   PASID translation                        { 0 }
2024-12-22T18:47:20.642Z In(05) vmx   Mode-based execute control for EPT       {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Sub-page write permissions for EPT       { 0 }
2024-12-22T18:47:20.642Z In(05) vmx   PT uses guest physical addresses         {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Use TSC scaling                          {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Enable UMWAIT and TPAUSE                 {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Enable ENCLV in VMX non-root mode        { 0 }
2024-12-22T18:47:20.642Z In(05) vmx   Enable EPC Virtualization Extensions     { 0 }
2024-12-22T18:47:20.642Z In(05) vmx   Bus lock exiting                         { 0 }
2024-12-22T18:47:20.642Z In(05) vmx   Notification VM exits                    { 0 }
2024-12-22T18:47:20.642Z In(05) vmx Tertiary Processor-Based VM-Execution Controls (0x0000000000000001)
2024-12-22T18:47:20.642Z In(05) vmx   LOADIWKEY exiting                         yes
2024-12-22T18:47:20.642Z In(05) vmx   Enable HLAT                                no
2024-12-22T18:47:20.642Z In(05) vmx   Enable Paging-Write                        no
2024-12-22T18:47:20.642Z In(05) vmx   Enable Guest Paging Verification           no
2024-12-22T18:47:20.642Z In(05) vmx   Enable IPI Virtualization                  no
2024-12-22T18:47:20.642Z In(05) vmx True VM-Exit Controls (0xf77fffff00036dfb)
2024-12-22T18:47:20.642Z In(05) vmx   Save debug controls                      {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Host address-space size                  {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Acknowledge interrupt on exit            {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Save IA32_PAT                            {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Load IA32_PAT                            {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Save IA32_EFER                           {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Load IA32_EFER                           {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Save VMX-preemption timer                {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Clear IA32_BNDCFGS                       { 0 }
2024-12-22T18:47:20.642Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Clear IA32_RTIT MSR                      {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Clear IA32_LBR_CTL MSR                   {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Clear user-interrupt notification vector { 0 }
2024-12-22T18:47:20.642Z In(05) vmx   Load CET state                           {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Load PKRS                                {0,1}
2024-12-22T18:47:20.642Z In(05) vmx True VM-Entry Controls (0x0076ffff000011fb)
2024-12-22T18:47:20.642Z In(05) vmx   Load debug controls                      {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   IA-32e mode guest                        {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Entry to SMM                             {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Deactivate dual-monitor mode             {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Load IA32_PERF_GLOBAL_CTRL               {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Load IA32_PAT                            {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Load IA32_EFER                           {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Load IA32_BNDCFGS                        { 0 }
2024-12-22T18:47:20.642Z In(05) vmx   Conceal VMX from processor trace         {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Load IA32_RTIT MSR                       {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Load user-interrupt notification vector  { 0 }
2024-12-22T18:47:20.642Z In(05) vmx   Load CET state                           {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Load IA32_LBR_CTL MSR                    {0,1}
2024-12-22T18:47:20.642Z In(05) vmx   Load PKRS                                {0,1}
2024-12-22T18:47:20.642Z In(05) vmx VPID and EPT Capabilities (0x00000f0106f34141)
2024-12-22T18:47:20.642Z In(05) vmx   R=0/W=0/X=1                               yes
2024-12-22T18:47:20.642Z In(05) vmx   Page-walk length 3                        yes
2024-12-22T18:47:20.642Z In(05) vmx   EPT memory type WB                        yes
2024-12-22T18:47:20.642Z In(05) vmx   2MB super-page                            yes
2024-12-22T18:47:20.642Z In(05) vmx   1GB super-page                            yes
2024-12-22T18:47:20.642Z In(05) vmx   INVEPT support                            yes
2024-12-22T18:47:20.642Z In(05) vmx   Access & Dirty Bits                       yes
2024-12-22T18:47:20.642Z In(05) vmx   Advanced VM exit information for EPT violations   yes
2024-12-22T18:47:20.642Z In(05) vmx   Supervisor shadow-stack control           yes
2024-12-22T18:47:20.642Z In(05) vmx   Type 1 INVEPT                             yes
2024-12-22T18:47:20.642Z In(05) vmx   Type 2 INVEPT                             yes
2024-12-22T18:47:20.642Z In(05) vmx   INVVPID support                           yes
2024-12-22T18:47:20.642Z In(05) vmx   Type 0 INVVPID                            yes
2024-12-22T18:47:20.642Z In(05) vmx   Type 1 INVVPID                            yes
2024-12-22T18:47:20.642Z In(05) vmx   Type 2 INVVPID                            yes
2024-12-22T18:47:20.642Z In(05) vmx   Type 3 INVVPID                            yes
2024-12-22T18:47:20.642Z In(05) vmx Miscellaneous VMX Data (0x000000007004c1e7)
2024-12-22T18:47:20.642Z In(05) vmx   TSC to preemption timer ratio      7
2024-12-22T18:47:20.642Z In(05) vmx   VM-Exit saves EFER.LMA           yes
2024-12-22T18:47:20.642Z In(05) vmx   Activity State HLT               yes
2024-12-22T18:47:20.642Z In(05) vmx   Activity State shutdown          yes
2024-12-22T18:47:20.642Z In(05) vmx   Activity State wait-for-SIPI     yes
2024-12-22T18:47:20.642Z In(05) vmx   Processor trace in VMX           yes
2024-12-22T18:47:20.642Z In(05) vmx   RDMSR SMBASE MSR in SMM          yes
2024-12-22T18:47:20.642Z In(05) vmx   CR3 targets supported              4
2024-12-22T18:47:20.642Z In(05) vmx   Maximum MSR list size            512
2024-12-22T18:47:20.642Z In(05) vmx   VMXOFF holdoff of SMIs           yes
2024-12-22T18:47:20.642Z In(05) vmx   Allow all VMWRITEs               yes
2024-12-22T18:47:20.642Z In(05) vmx   Allow zero instruction length    yes
2024-12-22T18:47:20.642Z In(05) vmx   MSEG revision ID                   0
2024-12-22T18:47:20.642Z In(05) vmx VMX-Fixed Bits in CR0 (0x0000000080000021/0x00000000ffffffff)
2024-12-22T18:47:20.642Z In(05) vmx   Fixed to 0        0xffffffff00000000
2024-12-22T18:47:20.642Z In(05) vmx   Fixed to 1        0x0000000080000021
2024-12-22T18:47:20.642Z In(05) vmx   Variable          0x000000007fffffde
2024-12-22T18:47:20.642Z In(05) vmx VMX-Fixed Bits in CR4 (0x0000000000002000/0x0000000001ff2fff)
2024-12-22T18:47:20.642Z In(05) vmx   Fixed to 0        0xfffffffffe00d000
2024-12-22T18:47:20.642Z In(05) vmx   Fixed to 1        0x0000000000002000
2024-12-22T18:47:20.642Z In(05) vmx   Variable          0x0000000001ff0fff
2024-12-22T18:47:20.642Z In(05) vmx VMCS Enumeration (0x000000000000002e)
2024-12-22T18:47:20.642Z In(05) vmx   Highest index                   0x17
2024-12-22T18:47:20.642Z In(05) vmx VM Functions (0x0000000000000001)
2024-12-22T18:47:20.642Z In(05) vmx   Function  0 (EPTP-switching) supported.
2024-12-22T18:47:20.642Z In(05) vmx Monitor_PowerOn: hostedVSMPMaxSkew is 1500 us (3744003 cycles)
2024-12-22T18:47:20.643Z In(05) vmx vmm-modules: [vmm.vmm, vmce-vmce.vmm, viommu-none.vmm, vprobe-none.vmm, hv-vt.vmm, gphys-ept.vmm, callstack-none.vmm, gi-none.vmm, gmm-none.vmm, !e1000Shared=0x0, !tdxSharedVMData=0x880, !vmSamples=0x880, !theIOSpace=0x8c0, !ttGPPerVcpu=0x76c0, {UseUnwind}=0x0, numVCPUsAsAddr=0x2, {SharedAreaReservations}=0x7700, {rodataSize}=0x20580, {textAddr}=0xfffffffffc000000, {textSize}=0x8d7d7, <MonSrcFile>]
2024-12-22T18:47:20.643Z In(05) vmx vmm-vcpus:   2
2024-12-22T18:47:20.689Z In(05) vmx KHZEstimate 2496002
2024-12-22T18:47:20.689Z In(05) vmx MHZEstimate 2496
2024-12-22T18:47:20.689Z In(05) vmx NumVCPUs 2
2024-12-22T18:47:20.689Z In(05) vmx AIOGNRC: numThreads=17 ide=0, scsi=1, passthru=0
2024-12-22T18:47:20.689Z In(05) vmx WORKER: Creating new group with maxThreads=17 (17)
2024-12-22T18:47:20.698Z In(05) vmx WORKER: Creating new group with maxThreads=1 (18)
2024-12-22T18:47:20.698Z In(05) vmx MainMem: CPT Host WZ=0 PF=4096 D=0
2024-12-22T18:47:20.698Z In(05) vmx MainMem: CPT PLS=1 PLR=1 BS=1 BlkP=32 Mult=4 W=50
2024-12-22T18:47:20.699Z In(05) vmx MainMem: Opened paging file, 'H:\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\564d37ba-e7c1-1dc8-84ad-b863fcf3ccdd.vmem'.
2024-12-22T18:47:20.700Z In(05) vmx MStat: Creating Stat vm.uptime
2024-12-22T18:47:20.700Z In(05) vmx MStat: Creating Stat vm.suspendTime
2024-12-22T18:47:20.700Z In(05) vmx MStat: Creating Stat vm.powerOnTimeStamp
2024-12-22T18:47:20.701Z In(05) aioCompletion VTHREAD 5824 "aioCompletion"
2024-12-22T18:47:20.701Z In(05) vmx VMXAIOMGR: Using: simple=Compl
2024-12-22T18:47:20.706Z In(05) vmx WORKER: Creating new group with maxThreads=1 (19)
2024-12-22T18:47:20.711Z In(05) vmx WORKER: Creating new group with maxThreads=14 (33)
2024-12-22T18:47:20.712Z In(05) vmx FeatureCompat: No VM masks.
2024-12-22T18:47:20.712Z In(05) vmx TimeTracker host to guest rate conversion 981447750 @ 2496002000Hz -> 0 @ 2496002000Hz
2024-12-22T18:47:20.712Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + -981447750
2024-12-22T18:47:20.712Z In(05) vmx TSC scaling enabled.
2024-12-22T18:47:20.712Z In(05) vmx TSC offsetting enabled.
2024-12-22T18:47:20.712Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2024-12-22T18:47:20.712Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2024-12-22T18:47:20.712Z In(05) vmx MKS PowerOn
2024-12-22T18:47:20.713Z In(05) mks VTHREAD 5836 "mks"
2024-12-22T18:47:20.713Z In(05) mks MKS thread is alive
2024-12-22T18:47:20.713Z In(05) svga VTHREAD 7184 "svga"
2024-12-22T18:47:20.713Z In(05) svga SVGA thread is alive
2024-12-22T18:47:20.714Z In(05) mks MKS: SSE2=1, SSSE3=1, SSE4_1=1
2024-12-22T18:47:20.715Z In(05) mouse VTHREAD 6632 "mouse"
2024-12-22T18:47:20.715Z In(05) mks MKS-HookKeyboard: RegQueryValueEx(LowLevelHooksTimeout) failed: The system cannot find the file specified (2)
2024-12-22T18:47:20.715Z In(05) kbh VTHREAD 4788 "kbh"
2024-12-22T18:47:20.716Z In(05) mks MKS Win32: Registering top level window (0x40252) to receive session change notification.
2024-12-22T18:47:20.717Z In(05) mks Current Display Settings:
2024-12-22T18:47:20.717Z In(05) mks    Display: 0 size: 1920x1080  position: (0, 0) name: \\.\DISPLAY1  
2024-12-22T18:47:20.717Z In(05) mks MKS Win32: MIL: 0x3000
2024-12-22T18:47:20.717Z In(05) mks MKS-RenderMain: PowerOn allowed MKSBasicOps 
2024-12-22T18:47:20.717Z In(05) mks MKS-RenderMain: ISB enabled by config
2024-12-22T18:47:20.717Z In(05) mks MKS-RenderMain: Collecting RenderOps caps from MKSBasicOps
2024-12-22T18:47:20.717Z In(05) mks MKS-RenderMain: Starting MKSBasicOps
2024-12-22T18:47:20.717Z In(05) mks MKS-RenderMain: Started MKSBasicOps
2024-12-22T18:47:20.717Z In(05) mks MKS-RenderMain: Found Full Renderer: MKSBasicOps
2024-12-22T18:47:20.717Z In(05) mks MKS-RenderMain: maxTextureSize=32768
2024-12-22T18:47:20.719Z In(05) mks KHBKL: Unable to parse keystring at: ''
2024-12-22T18:47:20.719Z In(05) mks MKSRemoteMgr: Set default display name: Đại Chiến Quốc Mobile LouLx
2024-12-22T18:47:20.719Z In(05) mks MKSRemoteMgr: Loading VNC Configuration from VM config file
2024-12-22T18:47:20.719Z In(05) mks MKSRemoteMgr: Using default VNC keymap table "us"
2024-12-22T18:47:20.719Z In(05) vmx VLANCE: send cluster threshold is 80, size = 2 recalcInterval is 20000 us
2024-12-22T18:47:20.719Z In(05) vmx VMXNET: send cluster threshold is 80, size = 2 recalcInterval is 20000 ticks, dontClusterSize is 128
2024-12-22T18:47:20.720Z In(05) vmx Chipset version: 0x17
2024-12-22T18:47:20.721Z In(05) vmx SOUNDLIB: Creating the Wave sound backend.
2024-12-22T18:47:20.729Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "-1"
2024-12-22T18:47:20.729Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "-1"
2024-12-22T18:47:20.729Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "-1"
2024-12-22T18:47:20.729Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "-1"
2024-12-22T18:47:20.729Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "21"
2024-12-22T18:47:20.729Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "22"
2024-12-22T18:47:20.730Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "23"
2024-12-22T18:47:20.730Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "24"
2024-12-22T18:47:20.782Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation begins.
2024-12-22T18:47:20.782Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation completes.
2024-12-22T18:47:20.782Z No(00) vmx ConfigDB: Setting scsi0:0.redo = ""
2024-12-22T18:47:20.782Z In(05) vmx DISK: OPEN scsi0:0 'H:\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmdk' persistent R[]
2024-12-22T18:47:20.796Z In(05) vmx DISKLIB-LIB_MISC   : DiskLib_GetStorageBlockSizes: Failed to get storage block sizes, The virtual disk requires a feature not supported by this program.
2024-12-22T18:47:20.797Z In(05) vmx DiskGetGeometry: Reading of disk partition table
2024-12-22T18:47:20.810Z In(05) vmx DISK: Disk 'H:\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmdk' has UUID '60 00 c2 9b 7a 6f 2b 65-fe bb e5 fb 15 2c 5c cd'
2024-12-22T18:47:20.811Z In(05) vmx DISK: OPEN 'H:\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmdk' Geo (7832/255/63) BIOS Geo (7832/255/63)
2024-12-22T18:47:20.818Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Reported rotation rate = 5400
2024-12-22T18:47:20.818Z In(05) vmx DISK: Opening disks took 36 ms.
2024-12-22T18:47:20.818Z In(05) vmx VUsb powered on, but no USB controllers.
2024-12-22T18:47:20.837Z In(05) vmx SCSI: scsi0: intr coalescing: on period=50msec cifTh=4 iopsTh=2000 hlt=0
2024-12-22T18:47:20.837Z In(05) vmx SCSI0: UNTAGGED commands will be converted to ORDER tags.
2024-12-22T18:47:20.837Z In(05) vmx SCSI DEVICE (scsi0:0): Computed value of scsi0:0.useBounceBuffers: default
2024-12-22T18:47:20.837Z In(05) vmx DISKUTIL: scsi0:0 : capacity=125829120 logical sector size=512
2024-12-22T18:47:20.837Z In(05) vmx DISKUTIL: scsi0:0 : geometry=7832/255/63
2024-12-22T18:47:20.837Z In(05) vmx SVGA-GFB: Config settings: autodetect=1, numDisplays=1, maxWidth=2560, maxHeight=1600
2024-12-22T18:47:20.837Z In(05) vmx SVGA-GFB: Desired maximum display topology: wh(6688, 5016)
2024-12-22T18:47:20.837Z In(05) vmx SVGA-GFB: Autodetected target gfbSize = 268435456
2024-12-22T18:47:20.837Z In(05) vmx SVGA-GFB: Using Initial       gfbSize = 4194304
2024-12-22T18:47:20.837Z In(05) vmx SVGA-GFB: MaxPrimaryMem      register = 268435456
2024-12-22T18:47:20.837Z In(05) vmx SVGA-GFB: Truncated maximum resolution for register modes to VRAM size: VRAM=4194304 bytes, max wh(1176, 885)
2024-12-22T18:47:20.837Z In(05) vmx SVGA-GFB: Max wh(1176, 885), number of displays: 10
2024-12-22T18:47:20.837Z In(05) vmx SVGA-GFB: Allocated gfbSize=4194304
2024-12-22T18:47:20.837Z No(00) vmx ConfigDB: Setting vmotion.checkpointFBSize = "4194304"
2024-12-22T18:47:20.837Z No(00) vmx ConfigDB: Setting vmotion.checkpointSVGAPrimarySize = "268435456"
2024-12-22T18:47:20.837Z In(05) vmx SVGA: SVGA DeviceLabel: svga2
2024-12-22T18:47:20.837Z In(05) vmx SVGA: mobMaxSize=134217728
2024-12-22T18:47:20.837Z In(05) vmx SVGA: graphicsMemoryKB=262144
2024-12-22T18:47:20.838Z In(05) vmx SVGA: FIFO capabilities 0x0000077f
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.supports3D bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.baseCapsLevel num 11
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxPointSize num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureSize num 32768
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxVolumeExtent num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureAnisotropy num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.lineStipple bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxMaxConstantBuffers num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxProvokingVertex bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm41 bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample2x bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample4x bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.msFullQuality bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicOps bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.bc67 num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm5 bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample8x bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicBlendOps bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxForcedSampleCount num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (before clamping) svga.gl43 bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.supports3D bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.baseCapsLevel num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxPointSize num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureSize num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxVolumeExtent num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureAnisotropy num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.lineStipple bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxMaxConstantBuffers num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxProvokingVertex bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm41 bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample2x bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample4x bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.msFullQuality bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicOps bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.bc67 num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm5 bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample8x bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicBlendOps bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxForcedSampleCount num 0
2024-12-22T18:47:20.838Z In(05) vmx SVGAFeature renderer (after  clamping) svga.gl43 bool 0
2024-12-22T18:47:20.838Z In(05) vmx SVGA3dClamp: Renderer Provides     BC67Level:     0 (    0,     0)
2024-12-22T18:47:20.838Z In(05) vmx SVGA3dClamp: Renderer Provides BaseCapsLevel:     0 (    0,     0)
2024-12-22T18:47:20.838Z In(05) vmx SVGA3dClamp: Renderer Provides    ClampLevel:     0 (    0,     0)
2024-12-22T18:47:20.838Z In(05) vmx SVGA3dCaps: host, at power on (3d disabled)
2024-12-22T18:47:20.838Z In(05) vmx   cap[ 19]: 0x00002000 (MAX_TEXTURE_WIDTH)
2024-12-22T18:47:20.838Z In(05) vmx   cap[ 20]: 0x00002000 (MAX_TEXTURE_HEIGHT)
2024-12-22T18:47:20.838Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2024-12-22T18:47:20.838Z In(05) vmx SVGA3dClamp:     Host Provides     BC67Level:     0 (    0,     0)
2024-12-22T18:47:20.838Z In(05) vmx SVGA3dClamp:     Host Provides BaseCapsLevel:     0 (    0,     4)
2024-12-22T18:47:20.838Z In(05) vmx SVGA3dClamp:     Host Provides    ClampLevel:     0 (    0,     0)
2024-12-22T18:47:20.838Z In(05) vmx SVGA3dCaps: Disabling 3d support
2024-12-22T18:47:20.838Z In(05) vmx SVGA3dCaps: guest, compatibility level: 8
2024-12-22T18:47:20.838Z In(05) vmx   cap[ 19]: 0x00002000 (MAX_TEXTURE_WIDTH)
2024-12-22T18:47:20.838Z In(05) vmx   cap[ 20]: 0x00002000 (MAX_TEXTURE_HEIGHT)
2024-12-22T18:47:20.838Z In(05) vmx SVGA3dClamp:    Guest Requires     BC67Level:     0 (    0,     0)
2024-12-22T18:47:20.838Z In(05) vmx SVGA3dClamp:    Guest Requires BaseCapsLevel:     0 (    0,     0)
2024-12-22T18:47:20.838Z In(05) vmx SVGA3dClamp:    Guest Requires    ClampLevel:     0 (    0,     0)
2024-12-22T18:47:20.846Z In(05) vmx Ethernet0 MAC Address: 00:0c:29:d6:20:c7
2024-12-22T18:47:20.847Z No(00) vmx ConfigDB: Setting vmci0.id = "-1126817593"
2024-12-22T18:47:20.853Z In(05) vmx WORKER: Creating new group with maxThreads=1 (34)
2024-12-22T18:47:20.853Z In(05) vmx DISKUTIL: (null) : max toolsVersion = 0, type = 0
2024-12-22T18:47:20.853Z In(05) vmx DISKUTIL: Offline toolsVersion = 0, type = 0
2024-12-22T18:47:20.853Z In(05) vmx TOOLS setting legacy tools version to '0' type 0, manifest status is 9
2024-12-22T18:47:20.854Z In(05) vmx ToolsISO: Refreshing imageName for 'centos7-64' (refreshCount=1, lastCount=1).
2024-12-22T18:47:20.854Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-12-22T18:47:20.854Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-12-22T18:47:20.854Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2024-12-22T18:47:20.854Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'centos7-64' guest.
2024-12-22T18:47:20.854Z In(05) vmx TOOLS updated cached value for isoImageExists to 1.
2024-12-22T18:47:20.854Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'noTools', 'noTools', install possible
2024-12-22T18:47:20.854Z In(05) vmx ToolsISO: Refreshing imageName for 'centos7-64' (refreshCount=1, lastCount=1).
2024-12-22T18:47:20.854Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-12-22T18:47:20.854Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-12-22T18:47:20.855Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2024-12-22T18:47:20.855Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'centos7-64' guest.
2024-12-22T18:47:20.855Z In(05) vmx TOOLS updated cached value for isoImageExists to 1.
2024-12-22T18:47:20.855Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'noTools', 'noTools', install possible
2024-12-22T18:47:20.855Z In(05) vmx Tools: sending 'OS_PowerOn' (state = 3) state change request
2024-12-22T18:47:20.855Z In(05) vmx Tools: Delaying state change request to state 3.
2024-12-22T18:47:20.855Z In(05) vmx ToolsISO: Refreshing imageName for 'centos7-64' (refreshCount=1, lastCount=1).
2024-12-22T18:47:20.855Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-12-22T18:47:20.855Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-12-22T18:47:20.856Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2024-12-22T18:47:20.856Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'centos7-64' guest.
2024-12-22T18:47:20.856Z In(05) vmx MsgHint: msg.tools.toolsReminder
2024-12-22T18:47:20.856Z In(05)+ vmx Install the VMware Tools package inside this virtual machine. After the guest operating system starts, select VM > Install VMware Tools… and follow the instructions.
2024-12-22T18:47:20.856Z In(05)+ vmx ---------------------------------------
2024-12-22T18:47:20.867Z In(05) vmx TOOLS INSTALL initializing state to IDLE on power on.
2024-12-22T18:47:20.867Z In(05) vmx TOOLS INSTALL updating Rpc handlers registration.
2024-12-22T18:47:20.867Z In(05) vmx TOOLS INSTALL register RPC: upgrader.setGuestFileRoot
2024-12-22T18:47:20.867Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.is_image_inserted
2024-12-22T18:47:20.867Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.installerActive
2024-12-22T18:47:20.867Z In(05) vmx TOOLS INSTALL register RPC: guest.upgrader_send_cmd_line_args
2024-12-22T18:47:20.868Z In(05) vmx P9FS_PowerOn: 9PFS server is not enabled.
2024-12-22T18:47:20.868Z In(05) vmx HgfsServerManagerVigorInit: Initialize: dev api
2024-12-22T18:47:20.868Z In(05) vmx MKSVMX: Copy/paste enabled = 1
2024-12-22T18:47:20.868Z In(05) vmx DEPLOYPKG: No pending deploy package name set
2024-12-22T18:47:20.868Z In(05) vmx DEPLOYPKG: ToolsDeployPkgPublishState: state=0, code=0, message=(null)
2024-12-22T18:47:20.875Z In(05) vmx MonPmc: ctrBase 0x4c1 selBase 0x186/1 PGC 1/1 SMM 1 drain 0 AMD 0
2024-12-22T18:47:20.875Z In(05)+ vmx MonPmc:   gen counters num: 8 width 48 write width 48
2024-12-22T18:47:20.875Z In(05)+ vmx MonPmc:   fix counters num: 4 width 48; version 5
2024-12-22T18:47:20.875Z In(05)+ vmx MonPmc:   unavailable counters: 0xf0000003f
2024-12-22T18:47:20.892Z No(00) vmx ConfigDB: Setting monitor.phys_bits_used = "43"
2024-12-22T18:47:20.892Z In(05) vmx Full guest CPUID with differences from hostCPUID highlighted.
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest vendor: GenuineIntel
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest family: 0x6 model: 0x97 stepping: 0x5
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest codename: Alder Lake-S
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest name: 12th Gen Intel(R) Core(TM) i5-12400F
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID       level eaxIn, ecxIn:        eax        ebx        ecx        edx
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000000,  0: 0x00000020 0x756e6547 0x6c65746e 0x49656e69
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000001,  0: 0x00090675 0x00020800 0xf7fa3203 0x1f8bfbff
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 00000001,  0: 0x00090675 0x00100800 0x7ffafbbf 0xbfebfbff
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000002,  0: 0x00feff01 0x000000f0 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000004,  0: 0x04000121 0x02c0003f 0x0000003f 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 00000004,  0: 0x1c004121 0x02c0003f 0x0000003f 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000004,  1: 0x04000122 0x01c0003f 0x0000003f 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 00000004,  1: 0x1c004122 0x01c0003f 0x0000003f 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000004,  2: 0x04000143 0x0240003f 0x000007ff 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 00000004,  2: 0x1c004143 0x0240003f 0x000007ff 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000004,  3: 0x04004163 0x02c0003f 0x00005fff 0x00000004
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 00000004,  3: 0x1c03c163 0x02c0003f 0x00005fff 0x00000004
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000004,  4: 0x04000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 00000004,  4: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000006,  0: 0x00000004 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 00000006,  0: 0x00df8ff7 0x00000002 0x00000401 0x00000003
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000007,  0: 0x00000000 0x219c27eb 0x00000008 0xbc000400
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 00000007,  0: 0x00000001 0x239ca7eb 0x98c007ac 0xfc184410
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000007,  1: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 00000007,  1: 0x00401c10 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 0000000a,  0: 0x08300801 0x000000ff 0x0000000f 0x00008000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 0000000a,  0: 0x08300805 0x00000000 0x0000000f 0x00008604
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 0000000b,  0: 0x00000000 0x00000001 0x00000100 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 0000000b,  0: 0x00000001 0x00000002 0x00000100 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 0000000b,  1: 0x00000001 0x00000002 0x00000201 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 0000000b,  1: 0x00000004 0x0000000c 0x00000201 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 0000000d,  0: 0x00000207 0x00000a88 0x00000a88 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 0000000d,  0: 0x00000207 0x00000340 0x00000a88 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 0000000d,  1: 0x0000000b 0x000003d0 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 0000000d,  1: 0x0000000f 0x000003d0 0x00019900 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 0000000d,  2: 0x00000100 0x00000240 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 0000000d,  9: 0x00000008 0x00000a80 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000014,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 00000014,  0: 0x00000001 0x0000005f 0x00000007 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000014,  1: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 00000014,  1: 0x02490002 0x003f003f 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000015,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 00000015,  0: 0x00000002 0x00000082 0x0249f000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 00000016,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID *host level 00000016,  0: 0x000009c4 0x00001130 0x00000064 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 40000000,  0: 0x40000010 0x61774d56 0x4d566572 0x65726177
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 40000010,  0: 0x00261602 0x000101d0 0x00000000 0x00000000
2024-12-22T18:47:20.892Z In(05) vmx guest vs. host CPUID guest level 80000000,  0: 0x80000008 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.893Z In(05) vmx guest vs. host CPUID guest level 80000001,  0: 0x00000000 0x00000000 0x00000121 0x2c100800
2024-12-22T18:47:20.893Z In(05) vmx guest vs. host CPUID guest level 80000002,  0: 0x68743231 0x6e654720 0x746e4920 0x52286c65
2024-12-22T18:47:20.893Z In(05) vmx guest vs. host CPUID guest level 80000003,  0: 0x6f432029 0x54286572 0x6920294d 0x32312d35
2024-12-22T18:47:20.893Z In(05) vmx guest vs. host CPUID guest level 80000004,  0: 0x46303034 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.893Z In(05) vmx guest vs. host CPUID guest level 80000006,  0: 0x00000000 0x00000000 0x05007040 0x00000000
2024-12-22T18:47:20.893Z In(05) vmx guest vs. host CPUID guest level 80000007,  0: 0x00000000 0x00000000 0x00000000 0x00000100
2024-12-22T18:47:20.893Z In(05) vmx guest vs. host CPUID guest level 80000008,  0: 0x0000302b 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.893Z In(05) vmx guest vs. host CPUID *host level 80000008,  0: 0x00003027 0x00000000 0x00000000 0x00000000
2024-12-22T18:47:20.893Z In(05) vmx Minimum ucode level: 0x00000015
2024-12-22T18:47:20.893Z In(05) vmx VPMC: events will use hybrid freeze.
2024-12-22T18:47:20.893Z In(05) vmx VPMC: gen counters: num 8 mask 0xffffffffffff
2024-12-22T18:47:20.893Z In(05) vmx VPMC: fix counters: num 0 mask 0; version 1
2024-12-22T18:47:20.893Z In(05) vmx VPMC: hardware counters: 0
2024-12-22T18:47:20.893Z In(05) vmx VPMC: perf capabilities: 0x2000
2024-12-22T18:47:20.893Z In(05) vmx SVGA-PCI: BAR gfbSize=134217728, fifoSize=8388608
2024-12-22T18:47:20.893Z In(05) vmx SVGA: SVGA_REG_MEMORY_SIZE=4194304
2024-12-22T18:47:20.893Z In(05) vmx SVGA: SVGA_REG_VRAM_SIZE=4194304
2024-12-22T18:47:20.893Z In(05) vmx SVGA: Final Device caps : 0xfdff83e2
2024-12-22T18:47:20.893Z In(05) vmx SVGA: Final Device caps2: 0x00000007
2024-12-22T18:47:20.893Z In(05) vmx BusMemSampleSetUpStats: touched: initPct 75 pages 786432 : dirtied: initPct 75 pages 786432
2024-12-22T18:47:20.893Z In(05) vmx MemSched: caller 0 numvm 1 locked pages: num 2504 max 14640384
2024-12-22T18:47:20.893Z In(05) vmx MemSched: locked Page Limit: host 16184451 config 14648576
2024-12-22T18:47:20.893Z In(05) vmx MemSched: minmempct 50  timestamp 196
2024-12-22T18:47:20.893Z In(05) vmx MemSched: VM 0 min 537783 max 1062071 shares 1048576 paged 574477 nonpaged 5182 anonymous 8313 locked 2504 touchedPct 75 dirtiedPct 75 timestamp 196 vmResponsive is 1
2024-12-22T18:47:20.893Z In(05) vmx MemSched: locked 2504 target 1062071 balloon 0 0 0 swapped 0 0 allocd 0 512 state 0 100
2024-12-22T18:47:20.893Z In(05) vmx MemSched: states: 0 1 : 1 0 : 2 0 : 3 0
2024-12-22T18:47:20.893Z In(05) vmx MemSched: Balloon enabled 1 guestType 0 maxSize 0
2024-12-22T18:47:20.893Z In(05) vmx PStrIntern expansion: nBkts=256
2024-12-22T18:47:20.893Z In(05) vmx FeatureCompat: Capabilities:
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.sse3 = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.pclmulqdq = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.mwait = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.vmx = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.ssse3 = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.fma = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.cmpxchg16b = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.pcid = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.sse41 = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.sse42 = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.movbe = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.popcnt = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.aes = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.xsave = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.avx = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.f16c = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.rdrand = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.ds = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.ss = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.fsgsbase = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.bmi1 = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.avx2 = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.smep = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.bmi2 = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.enfstrg = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.invpcid = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.pqe = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.rdseed = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.adx = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.smap = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.clflushopt = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.clwb = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.sha = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.umip = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.pku = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.cet_ss = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.gfni = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.vaes = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.vpclmulqdq = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.rdpid = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.movdiri = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.movdir64b = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.pks = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.fast_short_repmov = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.mdclear = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.serialize = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.arch_lbr = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.cet_ibt = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.stibp = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.fcmd = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.ssbd = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.avx_vnni = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.fast_zero_movsb = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.fast_short_stosb = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.fast_short_cmpsb_scasb = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.xcr0_master_sse = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.xcr0_master_ymm_h = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.xcr0_master_bndregs = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.xcr0_master_bndcsr = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.xcr0_master_pkru = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.xsaveopt = 1
2024-12-22T18:47:20.893Z In(05) vmx Capability Found: cpuid.xsavec = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.xgetbv_ecx1 = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.xsaves = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.xss_master_cet_u = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.xss_master_cet_s = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.xsaves_cet_u_sup_by_xss = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.xsaves_cet_s_sup_by_xss = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.lbr_deep_cstate_reset = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.lbr_cpl_filtering = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.lbr_branch_filtering = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.lbr_call_stack_mode = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.lbr_mispredict = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.lbr_timed_lbrs = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.lbr_branch_type = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.lahf64 = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.abm = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.3dnprefetch = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.nx = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.pdpe1gb = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.rdtscp = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.lm = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.intel = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.ibrs = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.ibpb = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.lbr_depth_8 = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.lbr_depth_16 = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: cpuid.lbr_depth_32 = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: hv.capable = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vt.realmode = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vt.mbx = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: misc.cpuidfaulting = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vt.advexitinfo = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vt.eptad = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vt.ple = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vt.zeroinstlen = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: misc.rdcl_no = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: misc.ibrs_all = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: misc.rsba_no = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: misc.mds_no = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vt.ignserrcode = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vt.eptsss = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vpmc.microarchitecture.sapphirerapids = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vpmc.numgenctrs = 8
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vpmc.numfixedctrs = 4
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vpmc.genwidth = 0x30
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vpmc.fixedwidth = 0x30
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vpmc.version = 5
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vpmc.genctr.6 = 1
2024-12-22T18:47:20.894Z In(05) vmx Capability Found: vpmc.genctr.7 = 1
2024-12-22T18:47:20.894Z In(05) vmx FeatureCompat: Requirements:
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.sse3 - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.pclmulqdq - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.ssse3 - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.fma - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.cmpxchg16b - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.pcid - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.sse41 - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.sse42 - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.movbe - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.popcnt - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.aes - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.xsave - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.avx - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.f16c - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.rdrand - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.ss - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.fsgsbase - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.bmi1 - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.avx2 - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.smep - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.bmi2 - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.enfstrg - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.invpcid - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.rdseed - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.adx - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.smap - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.clflushopt - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.clwb - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.sha - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.pku - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.mdclear - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.stibp - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.fcmd - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.ssbd - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.xcr0_master_sse - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.xcr0_master_ymm_h - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.xcr0_master_pkru - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.xsaveopt - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.xsavec - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.xsaves - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.lahf64 - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.abm - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.3dnprefetch - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.nx - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.pdpe1gb - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.rdtscp - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.lm - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.intel - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.ibrs - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: cpuid.ibpb - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: misc.rdcl_no - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: misc.ibrs_all - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: misc.rsba_no - Bool:Min:1
2024-12-22T18:47:20.894Z In(05) vmx VM Features Required: misc.mds_no - Bool:Min:1
2024-12-22T18:47:20.896Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '1'
2024-12-22T18:47:20.896Z In(05) vmx 
2024-12-22T18:47:20.896Z In(05)+ vmx OvhdMem: Static (Power On) Overheads
2024-12-22T18:47:20.896Z In(05) vmx                                                       reserved      |          used
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  1048576 1048576      - |      0      0      -
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   16000  16000      - |      0      0      -
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem Total excluded                      :  1072256 1072256      - |      -      -      -
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem Actual maximum                      :         1072256        |             -
2024-12-22T18:47:20.896Z In(05)+ vmx 
2024-12-22T18:47:20.896Z In(05) vmx                                                       reserved      |          used
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       4      4      - |      0      0      -
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       2      2      - |      0      0      -
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :   98304  98304      - |      0      0      -
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :       6      6      - |      0      0      -
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :       4      4      - |      4      4      -
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2024-12-22T18:47:20.896Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :     130    130      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :    8704   8704      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :     678    678      - |    492    492      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  131072 131072      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    2560   2560      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   35840  35840      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem Total paged                         :  574477 574477      - |    506    506      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem Actual maximum                      :         574477        |        574477
2024-12-22T18:47:20.897Z In(05)+ vmx 
2024-12-22T18:47:20.897Z In(05) vmx                                                       reserved      |          used
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :     150    150      - |    135    135      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      37     37      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_PFrame                     :    2195   3266      - |   2195   2195      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |     16     16      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |     64     64      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      1      1      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      3      3      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       2      2      - |      2      2      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_SMM                        :      63     63      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :    1024   1024      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       2      2      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       2      2      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_LBR                        :       2      2      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      53     53      - |     53     53      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_MonNuma                    :     254    254      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem Total nonpaged                      :    4111   5630      - |   2469   2469      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem Actual maximum                      :           4111        |          5630
2024-12-22T18:47:20.897Z In(05)+ vmx 
2024-12-22T18:47:20.897Z In(05) vmx                                                       reserved      |          used
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     196    196      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    1090   1147      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      16     16      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       8      8      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       2      2      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      80     80      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_TC                          :    1026   1026      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       6      6      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       9      9      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       4      4      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_HV                          :       2      2      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       2      2      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_VHV                         :       6      6      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_Numa                        :      30     30      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_NumaTextRodata              :     200    200      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_NumaDataBss                 :      54     54      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      58     58      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2303   2303      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     465    465      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    2318   2318      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     266    266      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       7      7      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem Total anonymous                     :    8256   8313      - |      0      0      -
2024-12-22T18:47:20.897Z In(05) vmx OvhdMem Actual maximum                      :           8256        |          8313
2024-12-22T18:47:20.897Z In(05)+ vmx 
2024-12-22T18:47:20.897Z In(05) vmx VMMEM: Precise Reservation: 2292MB (MainMem=4096MB)
2024-12-22T18:47:20.897Z In(05) vmx VMXSTATS: Registering 4 stats: vmx.overheadMemSize
2024-12-22T18:47:20.897Z In(05) vmx Vix: [mainDispatch.c:1058]: VMAutomation_PowerOn. Powering on.
2024-12-22T18:47:20.898Z In(05) vmx VMX_PowerOn: ModuleTable_PowerOn = 1
2024-12-22T18:47:20.898Z No(00) vmx ConfigDB: Setting cleanShutdown = "FALSE"
2024-12-22T18:47:20.898Z No(00) vmx ConfigDB: Setting softPowerOff = "FALSE"
2024-12-22T18:47:20.946Z In(05) vcpu-0 VTHREAD 14140 "vcpu-0"
2024-12-22T18:47:20.950Z In(05) vcpu-0 MonTimer APIC:0/0 vec: 0
2024-12-22T18:47:20.950Z In(05) vcpu-0 APIC: version = 0x15, max LVT = 6, LDR = 0x2, DFR = 0xffffffff
2024-12-22T18:47:20.950Z In(05) vcpu-0 Active HV capabilities
2024-12-22T18:47:20.950Z In(05) vcpu-0    Virtual interrupt delivery
2024-12-22T18:47:20.950Z In(05) vcpu-0    XAPIC MMIO virtualization
2024-12-22T18:47:20.950Z In(05) vcpu-0    Full decode
2024-12-22T18:47:20.950Z In(05) vcpu-0    Nested paging A/D bits
2024-12-22T18:47:20.950Z In(05) vcpu-0    Real-address mode
2024-12-22T18:47:20.950Z In(05) vcpu-0    Skip debug state
2024-12-22T18:47:20.950Z In(05) vcpu-0    X2APIC virtualization
2024-12-22T18:47:20.951Z In(05) vcpu-0    TPR MMIO virtualization
2024-12-22T18:47:20.951Z In(05) vcpu-0    Page-modification logging
2024-12-22T18:47:20.951Z In(05) vcpu-0    PAUSE-loop exiting
2024-12-22T18:47:20.951Z In(05) vcpu-0    TSC scaling
2024-12-22T18:47:20.951Z In(05) vcpu-0    Advanced exit information for EPT violations
2024-12-22T18:47:20.951Z In(05) vcpu-0    Mode-based execute control for nested paging
2024-12-22T18:47:20.951Z In(05) vcpu-0    EPT-violation virtualization exception
2024-12-22T18:47:20.951Z In(05) vcpu-0    Event injection with instruction length zero
2024-12-22T18:47:20.951Z In(05) vcpu-0    PKS VM entry/exit controls
2024-12-22T18:47:20.951Z In(05) vcpu-0    CET Shadow Stacks
2024-12-22T18:47:20.951Z In(05) vcpu-0    Enable User Monitor and Mwait
2024-12-22T18:47:20.954Z In(05) vcpu-0 TSC scaling ratio: 0001_000000000000 (mult=2147483648, shift=31)
2024-12-22T18:47:20.954Z In(05) vcpu-0 CPU reset: hard (mode Emulation)
2024-12-22T18:47:20.955Z In(05) vcpu-1 VTHREAD 14288 "vcpu-1"
2024-12-22T18:47:20.955Z In(05) vcpu-1 MonTimer APIC:0/0 vec: 0
2024-12-22T18:47:20.955Z In(05) vcpu-1 APIC: version = 0x15, max LVT = 6, LDR = 0x2, DFR = 0xffffffff
2024-12-22T18:47:20.955Z In(05) vcpu-1 TSC scaling ratio: 0001_000000000000 (mult=2147483648, shift=31)
2024-12-22T18:47:20.955Z In(05) vcpu-1 CPU reset: hard (mode Emulation)
2024-12-22T18:47:20.956Z In(05) vcpu-0 GuestRpc: Successfully created RPCI listening socket.
2024-12-22T18:47:20.956Z In(05) vcpu-0 GuestRpc: Using vsocket for TCLO messaging is disabled.
2024-12-22T18:47:20.956Z In(05) vcpu-0 memoryHotplug: Node 0: Present: 4095 MB (100 %) Size:65535 MB (100 %)
2024-12-22T18:47:20.956Z In(05) vcpu-0 PIIX4: PM Resuming from suspend type 0x0, chipset.onlineStandby 0
2024-12-22T18:47:20.956Z In(05) vcpu-0 VNET: 'ethernet0' enable link state propagation, lsp.state = 5
2024-12-22T18:47:20.956Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'ethernet0' lsp.state = 4
2024-12-22T18:47:20.956Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'Ethernet0' notify available.
2024-12-22T18:47:20.957Z In(05) vcpu-0 HGFSPublish: publishing 0 shares
2024-12-22T18:47:20.957Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.accelerometer"
2024-12-22T18:47:20.957Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.ambientLight"
2024-12-22T18:47:20.957Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.compass"
2024-12-22T18:47:20.957Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.gyrometer"
2024-12-22T18:47:20.957Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.inclinometer"
2024-12-22T18:47:20.957Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.orientation"
2024-12-22T18:47:20.958Z In(05) vcpu-0 Win32U_GetFileAttributes: GetFileAttributesExW("H:\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmpl", ...) failed, error: 2
2024-12-22T18:47:20.958Z In(05) vcpu-0 PolicyVMXFindPolicyKey: policy file does not exist.
2024-12-22T18:47:20.960Z In(05) vcpu-0 DEVSWAP: GuestOS does not require LSI adapter swap.
2024-12-22T18:47:20.961Z In(05) vcpu-0 Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1872, success=1 additionalError=0
2024-12-22T18:47:20.961Z In(05) vcpu-0 Vix: [mainDispatch.c:4130]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=0, err=0).
2024-12-22T18:47:20.961Z In(05) vcpu-0 Vix: [mainDispatch.c:4130]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2024-12-22T18:47:20.961Z In(05) vcpu-0 Transitioned vmx/execState/val to poweredOn
2024-12-22T18:47:20.961Z In(05) vcpu-0 Tools: Adding Tools inactivity timer.
2024-12-22T18:47:20.961Z In(05) vcpu-0 Intel VT: FlexPriority enabled, VPID enabled.
2024-12-22T18:47:20.961Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2024-12-22T18:47:20.961Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 4 to 6.
2024-12-22T18:47:20.985Z In(05) mks MKSControlMgr: connected
2024-12-22T18:47:20.988Z In(05) mks MKS-VMDB: VMDB requested a screenshot
2024-12-22T18:47:20.988Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2024-12-22T18:47:20.989Z In(05) vmx [msg.dictionary.badEncodedOutput] Value "Đại Chiến Quốc Mobile LouLx" for variable "displayName" is not valid in the "windows-1252" encoding.
2024-12-22T18:47:20.989Z In(05) vmx Dictionary_WriteToBuffer: upgrading encoding from windows-1252 to UTF-8
2024-12-22T18:47:20.992Z In(05) mks KHBKL: Unable to parse keystring at: ''
2024-12-22T18:47:20.996Z In(05) vmx [msg.dictionary.badEncodedOutput] Value "Đại Chiến Quốc Mobile LouLx" for variable "displayName" is not valid in the "windows-1252" encoding.
2024-12-22T18:47:20.996Z In(05) vmx Dictionary_WriteToBuffer: upgrading encoding from windows-1252 to UTF-8
2024-12-22T18:47:20.998Z In(05) svga SWBScreen: Screen 0 Defined: xywh(0, 0, 640, 480) flags=0x3
2024-12-22T18:47:20.999Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-12-22T18:47:20.999Z In(05) mks SWBWindow: Window 1 Defined: src screenId=-1, src xywh(0, 0, 640, 480) dest xywh(0, 0, 640, 480) pixelScale=1, flags=0x10
2024-12-22T18:47:21.000Z In(05) mks GDI-Backend: successfully started by HWinMux to do window composition.
2024-12-22T18:47:21.003Z In(05) mks MKS-HWinMux: Started GDI presentation backend.
2024-12-22T18:47:21.012Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2024-12-22T18:47:21.012Z In(05) mks SWBWindow: Window 0 Defined: src screenId=-1, src xywh(0, 0, 640, 480) dest xywh(0, 0, 640, 480) pixelScale=1, flags=0xF
2024-12-22T18:47:21.088Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0x0) and 0xfe000000(0x0)
2024-12-22T18:47:21.088Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-12-22T18:47:21.091Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:21.091Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-12-22T18:47:21.189Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:21.189Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-12-22T18:47:21.193Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:21.193Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-12-22T18:47:21.193Z In(05) vcpu-0 SVGA: Registering IOSpace at 0x1070
2024-12-22T18:47:21.193Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:21.193Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-12-22T18:47:21.195Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.195Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.196Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.196Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.196Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.196Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.196Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.197Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.197Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.197Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.197Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.197Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.198Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.198Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.198Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.198Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.199Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.199Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.199Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.199Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.199Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.200Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.200Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.200Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.200Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.200Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.201Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.201Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.201Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.202Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.202Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.202Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.204Z In(05) vcpu-1 CPU reset: soft (mode Emulation)
2024-12-22T18:47:21.215Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-12-22T18:47:21.218Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-12-22T18:47:21.242Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 6 to 1.
2024-12-22T18:47:21.306Z In(05) vcpu-0 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.306Z In(05) vcpu-0 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.306Z In(05) vcpu-0 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.306Z In(05) vcpu-0 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.306Z In(05) vcpu-0 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.307Z In(05) vcpu-0 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.307Z In(05) vcpu-0 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.307Z In(05) vcpu-0 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.307Z In(05) vcpu-0 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.308Z In(05) vcpu-0 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.308Z In(05) vcpu-0 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.308Z In(05) vcpu-0 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.308Z In(05) vcpu-0 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.308Z In(05) vcpu-0 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.309Z In(05) vcpu-0 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.309Z In(05) vcpu-0 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.309Z In(05) vcpu-0 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.309Z In(05) vcpu-0 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.309Z In(05) vcpu-0 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.310Z In(05) vcpu-0 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.310Z In(05) vcpu-0 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.310Z In(05) vcpu-0 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.310Z In(05) vcpu-0 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.310Z In(05) vcpu-0 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.311Z In(05) vcpu-0 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.311Z In(05) vcpu-0 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.311Z In(05) vcpu-0 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.311Z In(05) vcpu-0 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.311Z In(05) vcpu-0 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.312Z In(05) vcpu-0 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.313Z In(05) vcpu-0 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.313Z In(05) vcpu-0 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:21.318Z In(05) vcpu-0 DISKUTIL: scsi0:0 : geometry=7832/255/63
2024-12-22T18:47:21.318Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=125829120 logical sector size=512
2024-12-22T18:47:21.538Z In(05) vcpu-1 CPU reset: soft (mode Emulation)
2024-12-22T18:47:21.543Z In(05) vcpu-0 BIOS-UUID is 56 4d 08 42 91 55 78 d0-35 2b 25 5c bc d6 20 c7
2024-12-22T18:47:21.660Z In(05) svga SWBScreen: Screen 0 Resized: xywh(0, 0, 720, 400) flags=0x3
2024-12-22T18:47:26.592Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 1 to 5.
2024-12-22T18:47:28.220Z In(05) vcpu-1 CPU reset: soft (mode Emulation)
2024-12-22T18:47:28.342Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2024-12-22T18:47:28.343Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.343Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.343Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2024-12-22T18:47:28.343Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.343Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.343Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2024-12-22T18:47:28.343Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.343Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.343Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2024-12-22T18:47:28.343Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.343Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.343Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.344Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2024-12-22T18:47:28.345Z In(05) vcpu-1 SVGA: FIFO is already mapped
2024-12-22T18:47:28.374Z In(05) vcpu-1 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.375Z In(05) vcpu-1 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.375Z In(05) vcpu-1 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.375Z In(05) vcpu-1 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.375Z In(05) vcpu-1 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.376Z In(05) vcpu-1 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.376Z In(05) vcpu-1 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.376Z In(05) vcpu-1 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.376Z In(05) vcpu-1 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.376Z In(05) vcpu-1 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.376Z In(05) vcpu-1 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.376Z In(05) vcpu-1 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.376Z In(05) vcpu-1 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.377Z In(05) vcpu-1 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.377Z In(05) vcpu-1 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.377Z In(05) vcpu-1 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.377Z In(05) vcpu-1 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.377Z In(05) vcpu-1 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.377Z In(05) vcpu-1 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.377Z In(05) vcpu-1 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.377Z In(05) vcpu-1 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.377Z In(05) vcpu-1 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.378Z In(05) vcpu-1 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.378Z In(05) vcpu-1 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.378Z In(05) vcpu-1 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.378Z In(05) vcpu-1 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.378Z In(05) vcpu-1 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.378Z In(05) vcpu-1 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.378Z In(05) vcpu-1 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.378Z In(05) vcpu-1 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.378Z In(05) vcpu-1 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.379Z In(05) vcpu-1 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.379Z In(05) vcpu-1 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.379Z In(05) vcpu-1 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.379Z In(05) vcpu-1 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.379Z In(05) vcpu-1 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.379Z In(05) vcpu-1 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.379Z In(05) vcpu-1 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.379Z In(05) vcpu-1 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.380Z In(05) vcpu-1 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.380Z In(05) vcpu-1 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.380Z In(05) vcpu-1 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.380Z In(05) vcpu-1 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.380Z In(05) vcpu-1 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.380Z In(05) vcpu-1 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.380Z In(05) vcpu-1 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.380Z In(05) vcpu-1 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.381Z In(05) vcpu-1 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.381Z In(05) vcpu-1 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.381Z In(05) vcpu-1 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.381Z In(05) vcpu-1 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.381Z In(05) vcpu-1 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.381Z In(05) vcpu-1 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.381Z In(05) vcpu-1 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.381Z In(05) vcpu-1 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.382Z In(05) vcpu-1 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.382Z In(05) vcpu-1 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.382Z In(05) vcpu-1 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.382Z In(05) vcpu-1 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.382Z In(05) vcpu-1 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.382Z In(05) vcpu-1 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.382Z In(05) vcpu-1 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.382Z In(05) vcpu-1 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.383Z In(05) vcpu-1 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.384Z In(05) vcpu-1 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.465Z In(05) vcpu-1 PCIBridge4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.465Z In(05) vcpu-1 pciBridge4:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.465Z In(05) vcpu-1 pciBridge4:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.465Z In(05) vcpu-1 pciBridge4:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.465Z In(05) vcpu-1 pciBridge4:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.465Z In(05) vcpu-1 pciBridge4:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.465Z In(05) vcpu-1 pciBridge4:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.465Z In(05) vcpu-1 pciBridge4:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.465Z In(05) vcpu-1 PCIBridge5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.465Z In(05) vcpu-1 pciBridge5:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 pciBridge5:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 pciBridge5:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 pciBridge5:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 pciBridge5:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 pciBridge5:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 pciBridge5:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 PCIBridge6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 pciBridge6:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 pciBridge6:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 pciBridge6:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 pciBridge6:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 pciBridge6:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 pciBridge6:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 pciBridge6:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.466Z In(05) vcpu-1 PCIBridge7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.467Z In(05) vcpu-1 pciBridge7:1: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.467Z In(05) vcpu-1 pciBridge7:2: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.467Z In(05) vcpu-1 pciBridge7:3: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.467Z In(05) vcpu-1 pciBridge7:4: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.467Z In(05) vcpu-1 pciBridge7:5: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.467Z In(05) vcpu-1 pciBridge7:6: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.467Z In(05) vcpu-1 pciBridge7:7: ISA/VGA decoding enabled (ctrl 0004)
2024-12-22T18:47:28.848Z In(05) vcpu-0 VMMouse: CMD Read ID
2024-12-22T18:47:28.850Z In(05) vcpu-0 Guest MSR write (0x49: 0x1)
2024-12-22T18:47:29.167Z In(05) vcpu-0 SCSI0: RESET BUS
2024-12-22T18:47:29.252Z In(05) vcpu-0 SCSI0: RESET BUS
2024-12-22T18:47:29.255Z In(05) vcpu-0 SVGA: FIFO is already mapped
2024-12-22T18:47:29.256Z In(05) svga SVGA hiding SVGA
2024-12-22T18:47:29.266Z In(05) vcpu-0 Guest: vmwgfx: In Tree-Unknown
2024-12-22T18:47:29.266Z In(05) vcpu-0 Guest: vmwgfx: Module Version: 2.15.0
2024-12-22T18:47:29.266Z In(05) svga SVGA enabling SVGA
2024-12-22T18:47:29.266Z In(05) svga SWBScreen: Screen 0 Destroyed: xywh(0, 0, 720, 400) flags=0x3
2024-12-22T18:47:29.267Z In(05) svga SVGA-ScreenMgr: Screen type changed to RegisterMode
2024-12-22T18:47:29.267Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 800, 480) flags=0x2
2024-12-22T18:47:29.278Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 800, 480) flags=0x2
2024-12-22T18:47:29.278Z In(05) svga SVGA-ScreenMgr: Screen type changed to ScreenTarget
2024-12-22T18:47:29.278Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 800, 600) flags=0x2
2024-12-22T18:47:29.320Z In(05) vcpu-0 Tools: Running status rpc handler: 0 => 1.
2024-12-22T18:47:29.320Z In(05) vcpu-0 Tools: Changing running status: 0 => 1.
2024-12-22T18:47:29.320Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 1 (last received 0s ago)
2024-12-22T18:47:29.320Z In(05) vcpu-0 Tools: Removing Tools inactivity timer.
2024-12-22T18:47:29.435Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=125829120 logical sector size=512
2024-12-22T18:47:29.436Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-12-22T18:47:29.436Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=125829120 logical sector size=512
2024-12-22T18:47:29.436Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-12-22T18:47:29.436Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=125829120 logical sector size=512
2024-12-22T18:47:29.436Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2024-12-22T18:47:29.754Z In(05) vcpu-1 DDB: "longContentID" = "8ba6d32365d448230510fd53c706494d" (was "c5162363846343e1504ba43dde70f488")
2024-12-22T18:47:50.319Z In(05) vcpu-0 Tools: Tools heartbeat timeout.
2024-12-22T18:47:50.319Z In(05) vcpu-0 Tools: Running status rpc handler: 1 => 0.
2024-12-22T18:47:50.319Z In(05) vcpu-0 Tools: Changing running status: 1 => 0.
2024-12-22T18:47:50.319Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 1 (last received 20s ago)
2024-12-22T18:47:54.276Z In(05) vcpu-0 Preparing for SPEC_CTRL Guest MSR write (0x48) passthrough.
2024-12-22T18:50:20.998Z In(05) vmx GuestRpcSendTimedOut: message to toolbox timed out.
2024-12-22T18:50:20.998Z In(05) vmx Vix: [guestCommands.c:1945]: Error VIX_E_TOOLS_NOT_RUNNING in VMAutomationTranslateGuestRpcError(): VMware Tools are not running in the guest
2024-12-22T18:53:55.279Z In(05) mks MKS-VMDB: VMDB requested a screenshot
2024-12-22T18:53:55.279Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2024-12-22T19:17:22.100Z In(05) vmx MemSched: caller 0 numvm 1 locked pages: num 676316 max 14640384
2024-12-22T19:17:22.100Z In(05) vmx MemSched: locked Page Limit: host 15886036 config 14648576
2024-12-22T19:17:22.100Z In(05) vmx MemSched: minmempct 50  timestamp 1997
2024-12-22T19:17:22.100Z In(05) vmx MemSched: VM 0 min 534350 max 1058638 shares 1048576 paged 574477 nonpaged 4111 anonymous 5951 locked 676316 touchedPct 6 dirtiedPct 2 timestamp 1997 vmResponsive is 1
2024-12-22T19:17:22.100Z In(05) vmx MemSched: locked 676316 target 1058638 balloon 0 0 627459 swapped 25692 0 allocd 1 512 state 0 100
2024-12-22T19:17:22.100Z In(05) vmx MemSched: states: 0 1801 : 1 0 : 2 0 : 3 0
2024-12-22T19:17:22.100Z In(05) vmx MemSched: Balloon enabled 1 guestType 1 maxSize 965322
2024-12-22T19:22:01.857Z In(05) vcpu-0 VMMouse: Disabling VMMouse mode
2024-12-22T19:22:01.905Z In(05) vcpu-0 PIIX4: PM Soft Off.  Good-bye.
2024-12-22T19:22:01.905Z In(05) vcpu-0 Chipset: The guest has requested that the virtual machine be powered off.
2024-12-22T19:22:01.905Z No(00) vcpu-0 ConfigDB: Setting softPowerOff = "TRUE"
2024-12-22T19:22:01.971Z In(05) vcpu-0 VMX: Issuing power-off request...
2024-12-22T19:22:01.971Z In(05) vmx Stopping VCPU threads...
2024-12-22T19:22:01.971Z In(05) vmx MKSThread: Requesting MKS exit
2024-12-22T19:22:01.971Z In(05) vmx Stopping MKS/SVGA threads
2024-12-22T19:22:01.971Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 800, 600) flags=0x2
2024-12-22T19:22:01.972Z In(05) svga SVGA thread is exiting the main loop
2024-12-22T19:22:01.972Z In(05) mks SWBWindow: Window 0 Destroyed: src screenId=-1, src xywh(0, 0, 800, 600) dest xywh(0, 0, 800, 600) pixelScale=1, flags=0xF
2024-12-22T19:22:01.972Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2024-12-22T19:22:01.972Z In(05) mks SWBWindow: Window 1 Destroyed: src screenId=-1, src xywh(0, 0, 800, 600) dest xywh(0, 0, 800, 600) pixelScale=1, flags=0x10
2024-12-22T19:22:01.972Z In(05) mks GDI-Backend: stopped by HWinMux to do window composition.
2024-12-22T19:22:01.972Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 0.
2024-12-22T19:22:01.974Z In(05) vmx MKS/SVGA threads are stopped
2024-12-22T19:22:01.974Z In(05) vmx 
2024-12-22T19:22:01.974Z In(05)+ vmx OvhdMem: Final (Power Off) Overheads
2024-12-22T19:22:01.974Z In(05) vmx                                                       reserved      |          used
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  1048576 1048576      - | 680298 680298      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7680   7680      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   16000  16000      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem Total excluded                      :  1072256 1072256      - |      -      -      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem Actual maximum                      :         1072256        |             -
2024-12-22T19:22:01.974Z In(05)+ vmx 
2024-12-22T19:22:01.974Z In(05) vmx                                                       reserved      |          used
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       4      4      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       2      2      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :   98304  98304      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :       6      6      - |      1      1      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_SvgaBESurfaceTable         :       4      4      - |      4      4      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :     130    130      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :    8704   8704      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      33     33      - |      1      1      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :     678    678      - |    492    492      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :  131072 131072      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0    469      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    2560   2560      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   35840  35840      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem Total paged                         :  574477 574477      - |    508    977      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem Actual maximum                      :         574477        |        574477
2024-12-22T19:22:01.974Z In(05)+ vmx 
2024-12-22T19:22:01.974Z In(05) vmx                                                       reserved      |          used
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :     150    150      - |    135    135      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      37     37      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_PFrame                     :    2195   3266      - |   2195   2195      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |     16     16      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |     64     64      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      1      1      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      3      3      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       2      2      - |      2      2      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      8      8      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      2      2      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |     57     57      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_SMM                        :      63     63      - |     40     40      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :    1024   1024      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      1      1      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      2      2      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       2      2      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       2      2      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_LBR                        :       2      2      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      53     53      - |     53     53      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_MonNuma                    :     254    254      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem Total nonpaged                      :    4111   5630      - |   2579   2579      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem Actual maximum                      :           4111        |          5630
2024-12-22T19:22:01.974Z In(05)+ vmx 
2024-12-22T19:22:01.974Z In(05) vmx                                                       reserved      |          used
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     196    196      - |     53    130      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    1090   1147      - |   1090   1090      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      16     16      - |     16     16      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      1      1      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      1      1      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       8      8      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       2      2      - |      1      1      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      80     80      - |     80     80      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_TC                          :    1026   1026      - |    952    952      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       6      6      - |      6      6      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       9      9      - |      6      6      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       4      4      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_HV                          :       2      2      - |      2      2      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       2      2      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_VHV                         :       6      6      - |      0      0      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_Numa                        :      30     30      - |     14     29      -
2024-12-22T19:22:01.974Z In(05) vmx OvhdMem OvhdMon_NumaTextRodata              :     200    375      - |    175    350      -
2024-12-22T19:22:01.975Z In(05) vmx OvhdMem OvhdMon_NumaDataBss                 :      54     54      - |     54     54      -
2024-12-22T19:22:01.975Z In(05) vmx OvhdMem OvhdMon_NumaLargeData               :       0    512      - |      0      0      -
2024-12-22T19:22:01.975Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      56     58      - |     44     44      -
2024-12-22T19:22:01.975Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :       0   2303      - |      0    332      -
2024-12-22T19:22:01.975Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     465    465      - |    223    223      -
2024-12-22T19:22:01.975Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    2318   2318      - |   1589   1589      -
2024-12-22T19:22:01.975Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     266    266      - |     69     69      -
2024-12-22T19:22:01.975Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |     96     96      -
2024-12-22T19:22:01.975Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       7      7      - |      0      0      -
2024-12-22T19:22:01.975Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2024-12-22T19:22:01.975Z In(05) vmx OvhdMem Total anonymous                     :    5951   9000      - |   4472   5071      -
2024-12-22T19:22:01.975Z In(05) vmx OvhdMem Actual maximum                      :           5951        |          8313
2024-12-22T19:22:01.975Z In(05)+ vmx 
2024-12-22T19:22:01.975Z In(05) vmx VMMEM: Maximum Reservation: 2301MB (MainMem=4096MB)
2024-12-22T19:22:01.975Z In(05) vmx MemSched: BALLOON HIST [0, 1048576]: 2080 2080 0 0 0 0 0 0 0 0 0 0
2024-12-22T19:22:01.975Z In(05) vmx MemSched: BALLOON P50 1 P70 1 P90 1 MIN 0 MAX 0
2024-12-22T19:22:01.975Z In(05) vmx MemSched: SWAP HIST [0, 1048576]: 49 2080 0 0 0 0 0 0 0 0 0 0
2024-12-22T19:22:01.975Z In(05) vmx MemSched: SWAP P50 10 P70 10 P90 10 MIN 0 MAX 31514
2024-12-22T19:22:01.975Z In(05) vmx MemSched: LOCK HIST [0, 1048576]: 0 8 11 6 17 4 14 2020 0 0 0 0
2024-12-22T19:22:01.975Z In(05) vmx MemSched: LOCK P50 70 P70 70 P90 70 MIN 2504 MAX 686297
2024-12-22T19:22:01.975Z In(05) vmx MemSched: LOCK_TARGET HIST [0, 1048576]: 0 0 0 0 0 0 0 0 0 0 2080 2080
2024-12-22T19:22:01.975Z In(05) vmx MemSched: LOCK_TARGET P50 100 P70 100 P90 100 MIN 1048576 MAX 1062071
2024-12-22T19:22:01.975Z In(05) vmx MemSched: ACTIVE_PCT HIST [0, 100]: 0 624 782 120 120 60 61 0 313 0 0 0
2024-12-22T19:22:01.975Z In(05) vmx MemSched: ACTIVE_PCT P50 20 P70 30 P90 80 MIN 4 MAX 75
2024-12-22T19:22:01.975Z In(05) vmx MemSched: NUM_VMS HIST [0, 10]: 0 0 2080 0 0 0 0 0 0 0 0 0
2024-12-22T19:22:01.975Z In(05) vmx MemSched: NUM_VMS P50 20 P70 20 P90 20 MIN 1 MAX 1
2024-12-22T19:22:01.975Z In(05) vmx MemSched: HOSTLOCK HIST [0, 14640384]: 0 2080 0 0 0 0 0 0 0 0 0 0
2024-12-22T19:22:01.975Z In(05) vmx MemSched: HOSTLOCK P50 10 P70 10 P90 10 MIN 2504 MAX 686297
2024-12-22T19:22:01.975Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '0'
2024-12-22T19:22:01.975Z In(05) vmx TOOLS received request in VMX to set option 'copypaste' -> '0'
2024-12-22T19:22:01.976Z In(05) vmx HgfsServerManagerVigorExit: Destroy:
2024-12-22T19:22:01.976Z In(05) vmx ToolsISO: Refreshing imageName for 'centos7-64' (refreshCount=1, lastCount=1).
2024-12-22T19:22:01.981Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2024-12-22T19:22:01.981Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2024-12-22T19:22:01.981Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2024-12-22T19:22:01.981Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'centos7-64' guest.
2024-12-22T19:22:01.981Z In(05) vmx TOOLS updated cached value for isoImageExists to 1.
2024-12-22T19:22:01.982Z In(05) vmx Tools: ToolsRunningStatus_Exit, delayedRequest is 0x23D997D2BD0
2024-12-22T19:22:01.982Z In(05) vmx Tools: [AppStatus] Last heartbeat value 1 (last received 2072s ago)
2024-12-22T19:22:01.982Z In(05) vmx TOOLS: appName=toolbox, oldStatus=0, status=0, guestInitiated=0.
2024-12-22T19:22:01.985Z In(05) vmx SOUNDLIB: Closing Wave sound backend.
2024-12-22T19:22:01.986Z In(05) mks MKSControlMgr: disconnected
2024-12-22T19:22:01.986Z In(05) mks MKS-RenderMain: Stopped MKSBasicOps
2024-12-22T19:22:01.986Z In(05) mks MKS PowerOff
2024-12-22T19:22:01.987Z In(05) svga SVGA thread is exiting
2024-12-22T19:22:01.987Z In(05) mks MKS thread is exiting
2024-12-22T19:22:01.987Z Wa(03) vmx 
2024-12-22T19:22:01.987Z In(05) vmx scsi0:0: numIOs = 19619 numMergedIOs = 3093 numSplitIOs = 393 (11.3%)
2024-12-22T19:22:01.987Z In(05) vmx Closing disk 'scsi0:0'
2024-12-22T19:22:01.994Z In(05) vmx AIOWIN32C: asyncOps=19908 syncOps=124 bufSize=312Kb fixedOps=6606
2024-12-22T19:22:01.994Z In(05) aioCompletion AIO thread processed 19908 completions
2024-12-22T19:22:02.140Z In(05) deviceThread Device thread is exiting
2024-12-22T19:22:02.140Z In(05) vmx Vix: [mainDispatch.c:1171]: VMAutomationPowerOff: Powering off.
2024-12-22T19:22:02.140Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("H:\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmpl", ...) failed, error: 2
2024-12-22T19:22:02.140Z In(05) vmx Policy_SavePolicyFile: invalid arguments to function.
2024-12-22T19:22:02.140Z In(05) vmx PolicyVMX_Exit: Could not write out policies: 15.
2024-12-22T19:22:02.140Z In(05) vmx WORKER: asyncOps=1 maxActiveOps=1 maxPending=1 maxCompleted=0
2024-12-22T19:22:02.141Z In(05) PowerNotifyThread PowerNotify thread exiting.
2024-12-22T19:22:02.345Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2024-12-22T19:22:02.345Z In(05) vmx Vix: [mainDispatch.c:4231]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2024-12-22T19:22:02.346Z In(05) vmx VMXSTATS: Ready to cleanup and munmap 23D98490000.
2024-12-22T19:22:02.346Z No(00) vmx ConfigDB: Setting cleanShutdown = "TRUE"
2024-12-22T19:22:02.394Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2024-12-22T19:22:02.394Z In(05) vmx Vix: [mainDispatch.c:4231]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2024-12-22T19:22:02.394Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2024-12-22T19:22:02.394Z In(05) vmx VMX idle exit
2024-12-22T19:22:02.394Z In(05) vmx WQPoolFreePoll : pollIx = 3, signalHandle = 1036
2024-12-22T19:22:02.394Z In(05) vmx Vix: [mainDispatch.c:817]: VMAutomation_LateShutdown()
2024-12-22T19:22:02.394Z In(05) vmx Vix: [mainDispatch.c:772]: VMAutomationCloseListenerSocket. Closing listener socket.
2024-12-22T19:22:02.395Z In(05) vmx Flushing VMX VMDB connections
2024-12-22T19:22:02.395Z In(05) vmx VmdbDbRemoveCnx: Removing Cnx from Db for '/db/connection/#1/'
2024-12-22T19:22:02.395Z In(05) vmx VmdbCnxDisconnect: Disconnect: closed pipe for pub cnx '/db/connection/#1/' (0)
2024-12-22T19:22:02.395Z In(05) vmx VigorTransport_ServerDestroy: server destroyed.
2024-12-22T19:22:02.395Z In(05) vmx WQPoolFreePoll : pollIx = 2, signalHandle = 828
2024-12-22T19:22:02.395Z In(05) vmx WQPoolFreePoll : pollIx = 1, signalHandle = 816
2024-12-22T19:22:02.398Z In(05) vmx VMX exit (0).
2024-12-22T19:22:02.398Z In(05) vmx OBJLIB-LIB: ObjLib cleanup done.
2024-12-22T19:22:02.398Z In(05) vmx AIOMGR-S : stat o=3 r=21 w=1 i=0 br=106992 bw=128
