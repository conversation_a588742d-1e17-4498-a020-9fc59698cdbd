# Disk DescriptorFile
version=1
encoding="GBK"
CID=5abb25eb
parentCID=ffffffff
createType="twoGbMaxExtentSparse"

# Extent description
RW 8323072 SPARSE "LouLx-Game-s001.vmdk"
RW 8323072 SPARSE "LouLx-Game-s002.vmdk"
RW 8323072 SPARSE "LouLx-Game-s003.vmdk"
RW 8323072 SPARSE "LouLx-Game-s004.vmdk"
RW 8323072 SPARSE "LouLx-Game-s005.vmdk"
RW 8323072 SPARSE "LouLx-Game-s006.vmdk"
RW 8323072 SPARSE "LouLx-Game-s007.vmdk"
RW 8323072 SPARSE "LouLx-Game-s008.vmdk"
RW 8323072 SPARSE "LouLx-Game-s009.vmdk"
RW 8323072 SPARSE "LouLx-Game-s010.vmdk"
RW 8323072 SPARSE "LouLx-Game-s011.vmdk"
RW 8323072 SPARSE "LouLx-Game-s012.vmdk"
RW 8323072 SPARSE "LouLx-Game-s013.vmdk"
RW 8323072 SPARSE "LouLx-Game-s014.vmdk"
RW 8323072 SPARSE "LouLx-Game-s015.vmdk"
RW 983040 SPARSE "LouLx-Game-s016.vmdk"

# The Disk Data Base 
#DDB

ddb.adapterType = "lsilogic"
ddb.geometry.cylinders = "7832"
ddb.geometry.heads = "255"
ddb.geometry.sectors = "63"
ddb.longContentID = "8f2940d66a5a5e70b5a764f55abb25eb"
ddb.uuid = "60 00 C2 9b 7a 6f 2b 65-fe bb e5 fb 15 2c 5c cd"
ddb.virtualHWVersion = "16"
