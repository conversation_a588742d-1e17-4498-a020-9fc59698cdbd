2025-05-31T07:48:15.170Z In(05) vmx Log for VMware Workstation pid=3160 version=17.6.1 build=build-24319023 option=Release
2025-05-31T07:48:15.170Z In(05) vmx The host is x86_64.
2025-05-31T07:48:15.170Z In(05) vmx Host codepage=windows-1252 encoding=windows-1252
2025-05-31T07:48:15.170Z In(05) vmx Host is Windows 11 Home, 64-bit (Build 26100.4202)
2025-05-31T07:48:15.170Z In(05) vmx Host offset from UTC is -07:00.
2025-05-31T07:48:15.143Z In(05) vmx VTHREAD 964 "vmx"
2025-05-31T07:48:15.149Z In(05) vmx LOCALE windows-1252 -> NULL User=42a System=409
2025-05-31T07:48:15.149Z In(05) vmx Msg_SetLocaleEx: HostLocale=windows-1252 UserLocale=NULL
2025-05-31T07:48:15.160Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2025-05-31T07:48:15.160Z In(05) vmx Msg_Reset:
2025-05-31T07:48:15.160Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2025-05-31T07:48:15.160Z In(05) vmx ----------------------------------------
2025-05-31T07:48:15.160Z In(05) vmx ConfigDB: Failed to load C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2025-05-31T07:48:15.161Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmpl", ...) failed, error: 2
2025-05-31T07:48:15.161Z In(05) vmx OBJLIB-LIB: Objlib initialized.
2025-05-31T07:48:15.163Z In(05) vmx DictionaryLoad: Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2025-05-31T07:48:15.163Z In(05) vmx [msg.dictionary.load.openFailed] Cannot open file "C:\Users\<USER>\AppData\Roaming\VMware\config.ini": The system cannot find the file specified.
2025-05-31T07:48:15.163Z In(05) vmx PREF Optional preferences file not found at C:\Users\<USER>\AppData\Roaming\VMware\config.ini. Using default values.
2025-05-31T07:48:15.167Z In(05) vmx lib/ssl: OpenSSL using default provider
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: Client usage
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: protocol list tls1.2
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: Server usage
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: protocol list tls1.2
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: protocol min 0x303 max 0x303
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: protocol list tls1.2 (openssl flags 0x36000000)
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: cipher list ECDHE+AESGCM:RSA+AESGCM:ECDHE+AES:RSA+AES
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: cipher suites TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384
2025-05-31T07:48:15.168Z In(05) vmx lib/ssl: curves list prime256v1:secp384r1:secp521r1
2025-05-31T07:48:15.181Z In(05) vmx Hostname=DESKTOP-OM5U9L4
2025-05-31T07:48:15.185Z In(05) vmx IP=fe80::31ab:904d:f158:d880%11
2025-05-31T07:48:15.185Z In(05) vmx IP=2402:800:63a3:bf1b:61dd:3692:ef18:eb49
2025-05-31T07:48:15.185Z In(05) vmx IP=2402:800:63a3:bf1b:2eeb:ae99:cc04:239
2025-05-31T07:48:15.185Z In(05) vmx IP=***********
2025-05-31T07:48:15.185Z In(05) vmx IP=fe80::be8f:f7c1:a4d8:d798%17
2025-05-31T07:48:15.185Z In(05) vmx IP=fe80::8b72:bb6d:62b8:b60c%8
2025-05-31T07:48:15.185Z In(05) vmx IP=*************
2025-05-31T07:48:15.185Z In(05) vmx IP=*************
2025-05-31T07:48:15.216Z In(05) vmx System uptime 11257541461 us
2025-05-31T07:48:15.216Z In(05) vmx Command line: "C:\Program Files (x86)\VMware\VMware Workstation\x64\vmware-vmx.exe" "-T" "querytoken" "-s" "vmx.stdio.keep=TRUE" "-#" "product=1;name=VMware Workstation;version=17.6.1;buildnumber=24319023;licensename=VMware Workstation;licenseversion=17.0;" "-@" "pipe=\\.\pipe\vmxa37e8e9eb824b24a;msgs=ui" "D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmx"
2025-05-31T07:48:15.216Z In(05) vmx Msg_SetLocaleEx: HostLocale=windows-1252 UserLocale=NULL
2025-05-31T07:48:15.239Z In(05) vmx WQPoolAllocPoll : pollIx = 1, signalHandle = 800
2025-05-31T07:48:15.239Z In(05) vmx WQPoolAllocPoll : pollIx = 2, signalHandle = 756
2025-05-31T07:48:15.240Z In(05) vmx VigorTransport listening on fd 876
2025-05-31T07:48:15.240Z In(05) vmx Vigor_Init 1
2025-05-31T07:48:15.240Z In(05) vmx Connecting 'ui' to pipe '\\.\pipe\vmxa37e8e9eb824b24a' with user '(null)'
2025-05-31T07:48:15.240Z In(05) vmx VMXVmdb: Local connection timeout: 60000 ms.
2025-05-31T07:48:15.340Z In(05) vmx VmdbAddConnection: cnxPath=/db/connection/#1/, cnxIx=1
2025-05-31T07:48:15.342Z In(05) vmx Vix: [mainDispatch.c:489]: VMAutomation: Initializing VMAutomation.
2025-05-31T07:48:15.343Z In(05) vmx Vix: [mainDispatch.c:741]: VMAutomationOpenListenerSocket() listening
2025-05-31T07:48:15.351Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-05-31T07:48:15.351Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2025-05-31T07:48:15.351Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-31T07:48:15.351Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1877, success=1 additionalError=0
2025-05-31T07:48:15.351Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=3, newAppState=1881, success=1 additionalError=0
2025-05-31T07:48:15.351Z In(05) vmx IOPL_Init: Hyper-V detected by CPUID
2025-05-31T07:48:15.353Z In(05) vmx IOCTL_VMX86_SET_MEMORY_PARAMS already set
2025-05-31T07:48:15.354Z In(05) vmx FeatureCompat: No EVC masks.
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID vendor: GenuineIntel
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID family: 0x6 model: 0xba stepping: 0x2
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID codename: Raptor Lake H/P/PX/U
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID name: 13th Gen Intel(R) CoreT i5-13500H
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000000, 0: 0x00000020 0x756e6547 0x6c65746e 0x49656e69
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000001, 0: 0x000b06a2 0x00400800 0xfffaf38b 0xbfcbfbff
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000002, 0: 0x00feff01 0x000000f0 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000003, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000004, 0: 0x7c004121 0x02c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000004, 1: 0x7c004122 0x01c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000004, 2: 0x7c01c143 0x0240003f 0x000007ff 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000004, 3: 0x7c0fc163 0x02c0003f 0x00005fff 0x00000004
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000004, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000005, 0: 0x00000040 0x00000040 0x00000003 0x00002020
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00000003
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000007, 0: 0x00000002 0x239c27a9 0x184007a4 0xbc18c410
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000007, 1: 0x00400810 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000007, 2: 0x00000000 0x00000000 0x00000000 0x00000011
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000008, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000a, 0: 0x07300605 0x00000000 0x00000007 0x00008603
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000b, 0: 0x00000001 0x00000002 0x00000100 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000b, 1: 0x00000006 0x00000010 0x00000201 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000b, 2: 0x00000000 0x00000000 0x00000002 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000d, 0: 0x00000007 0x00000340 0x00000340 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000d, 1: 0x0000000f 0x00000350 0x00001800 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000d, 2: 0x00000100 0x00000240 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000d, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000d, 4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000d, 5: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000d, 6: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000d, 7: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000d, 8: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000d, 9: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000d, a: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000d, b: 0x00000010 0x00000000 0x00000001 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000d, c: 0x00000018 0x00000000 0x00000001 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000f, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000000f, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000010, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000010, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000010, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000010, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000011, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000012, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000012, 1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000012, 2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000012, 3: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000013, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000014, 0: 0x00000001 0x0000005f 0x00000007 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000014, 1: 0x02490002 0x003f003f 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000015, 0: 0x00000002 0x000000a6 0x0249f000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000016, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000017, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000018, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000019, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000001a, 0: 0x40000001 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000001b, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000001c, 0: 0x4000000b 0x00000007 0x00000007 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000001d, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000001e, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000001f, 0: 0x00000001 0x00000002 0x00000100 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000001f, 1: 0x00000006 0x00000010 0x00000201 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 0000001f, 2: 0x00000000 0x00000000 0x00000002 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 00000020, 0: 0x00000000 0x00000001 0x00000000 0x00000000
2025-05-31T07:48:15.395Z In(05) vmx hostCPUID level 40000000, 0: 0x4000000c 0x7263694d 0x666f736f 0x76482074
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 40000001, 0: 0x31237648 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 40000002, 0: 0x000065f4 0x000a0000 0x00000004 0x0000106a
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 40000003, 0: 0x00003fff 0x002bb9ff 0x00000062 0x75fefbf6
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 40000004, 0: 0x00d60e14 0x00000000 0x0000002e 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 40000005, 0: 0x00000800 0x00000800 0x00000ba0 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 40000006, 0: 0x098200af 0x00000027 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 40000007, 0: 0x80000007 0x0000000b 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 40000008, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 40000009, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 4000000a, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 4000000b, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 4000000c, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 80000000, 0: 0x80000008 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 80000001, 0: 0x00000000 0x00000000 0x00000121 0x2c100800
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 80000002, 0: 0x68743331 0x6e654720 0x746e4920 0x52286c65
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 80000003, 0: 0x6f432029 0x20546572 0x312d3569 0x30303533
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 80000004, 0: 0x00000048 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 80000005, 0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 80000006, 0: 0x00000000 0x00000000 0x05007040 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 80000007, 0: 0x00000000 0x00000000 0x00000000 0x00000100
2025-05-31T07:48:15.396Z In(05) vmx hostCPUID level 80000008, 0: 0x00003027 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID differences from hostCPUID.
2025-05-31T07:48:15.396Z In(05) vmx CPUID[2] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00010003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[3] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00010003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[4] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00020003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[5] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00020003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[6] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00030003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[7] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00030003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[8] level 00000004, 0: 0x7c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[8] level 00000004, 1: 0x7c004122 0x01c0003f 0x0000007f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[8] level 00000004, 2: 0x7c01c143 0x03c0003f 0x000007ff 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[8] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00040003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[8] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[8] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[9] level 00000004, 0: 0x7c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[9] level 00000004, 1: 0x7c004122 0x01c0003f 0x0000007f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[9] level 00000004, 2: 0x7c01c143 0x03c0003f 0x000007ff 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[9] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00040003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[9] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[9] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[10] level 00000004, 0: 0x7c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[10] level 00000004, 1: 0x7c004122 0x01c0003f 0x0000007f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[10] level 00000004, 2: 0x7c01c143 0x03c0003f 0x000007ff 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[10] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00040003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[10] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[10] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[11] level 00000004, 0: 0x7c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[11] level 00000004, 1: 0x7c004122 0x01c0003f 0x0000007f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[11] level 00000004, 2: 0x7c01c143 0x03c0003f 0x000007ff 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[11] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00040003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[11] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[11] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[12] level 00000004, 0: 0x7c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[12] level 00000004, 1: 0x7c004122 0x01c0003f 0x0000007f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[12] level 00000004, 2: 0x7c01c143 0x03c0003f 0x000007ff 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[12] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00050003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[12] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[12] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[13] level 00000004, 0: 0x7c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[13] level 00000004, 1: 0x7c004122 0x01c0003f 0x0000007f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[13] level 00000004, 2: 0x7c01c143 0x03c0003f 0x000007ff 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[13] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00050003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[13] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[13] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[14] level 00000004, 0: 0x7c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[14] level 00000004, 1: 0x7c004122 0x01c0003f 0x0000007f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[14] level 00000004, 2: 0x7c01c143 0x03c0003f 0x000007ff 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[14] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00050003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[14] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[14] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[15] level 00000004, 0: 0x7c004121 0x01c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[15] level 00000004, 1: 0x7c004122 0x01c0003f 0x0000007f 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[15] level 00000004, 2: 0x7c01c143 0x03c0003f 0x000007ff 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[15] level 00000006, 0: 0x009f8ff3 0x00000002 0x00000409 0x00050003
2025-05-31T07:48:15.396Z In(05) vmx CPUID[15] level 00000014, 0: 0x00000001 0x0000005f 0x80000007 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx CPUID[15] level 0000001a, 0: 0x20000001 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.396Z In(05) vmx Physical APIC IDs: 0-1,8-9,16-17,24-25,32,34,36,38,40,42,44,46
2025-05-31T07:48:15.396Z In(05) vmx Physical X2APIC IDs: 0-1,8-9,16-17,24-25,32,34,36,38,40,42,44,46
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR       0x3a =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x480 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x481 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x482 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x483 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x484 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x485 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x486 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x487 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x488 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x489 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x48a =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x48b =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x48c =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x48d =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x48e =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x48f =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x490 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x491 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x492 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x493 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR 0xc0010114 =                  0
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR       0xce =         0x80000000
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x10a =           0x80e16f
2025-05-31T07:48:15.397Z In(05) vmx Common: MSR      0x122 =                  0
2025-05-31T07:48:15.397Z In(05) vmx TSC Hz estimates: vmmon 0, remembered 0, osReported 0. Using 3187200940 Hz.
2025-05-31T07:48:15.397Z In(05) vmx PTSC: RefClockToPTSC 0 @ 10000000Hz -> 0 @ 3187200940Hz
2025-05-31T07:48:15.397Z In(05) vmx PTSC: RefClockToPTSC ((x * 2673617930) >> 23) + -23675302243465
2025-05-31T07:48:15.397Z In(05) vmx PTSC: tscOffset 0
2025-05-31T07:48:15.397Z In(05) vmx PTSC: using user-level reference clock
2025-05-31T07:48:15.397Z In(05) vmx PTSC: hardware TSCs are synchronized.
2025-05-31T07:48:15.397Z In(05) vmx PTSC: hardware TSCs may have been adjusted by the host.
2025-05-31T07:48:15.397Z In(05) vmx PTSC: current PTSC=104859
2025-05-31T07:48:15.403Z In(05) vmx WQPoolAllocPoll : pollIx = 3, signalHandle = 972
2025-05-31T07:48:15.424Z In(05) vmx ConfigCheck: No rules file found. Checks are disabled.
2025-05-31T07:48:15.424Z In(05) vmx changing directory to D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\.
2025-05-31T07:48:15.424Z In(05) vmx Config file: D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmx
2025-05-31T07:48:15.424Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-31T07:48:15.424Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=2, newAppState=1878, success=1 additionalError=0
2025-05-31T07:48:15.425Z In(05) vmx LogRotation: Rotating to a new log file (keepOld 3) took 0.000391 seconds.
2025-05-31T07:48:15.450Z No(00) vmx LogVMXReplace: Successful switching from temporary to permanent log file took 0.026151 seconds.
2025-05-31T07:48:15.468Z Wa(03) vmx PowerOn
2025-05-31T07:48:15.468Z In(05) vmx VMX_PowerOn: VMX build 24319023, UI build 24319023
2025-05-31T07:48:15.468Z In(05) vmx HostWin32: WIN32 NUMA node 0, CPU mask 0x000000000000ffff
2025-05-31T07:48:15.472Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1871, success=1 additionalError=0
2025-05-31T07:48:15.472Z In(05) vmx STATSFILE: StatsFile 'D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.scoreboard' created
2025-05-31T07:48:15.473Z In(05) vmx HOST Windows version 10.0, build 26100, platform 2, ""
2025-05-31T07:48:15.473Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini
2025-05-31T07:48:15.473Z In(05) vmx DICT          printers.enabled = "FALSE"
2025-05-31T07:48:15.473Z In(05) vmx DICT --- NON PERSISTENT (null)
2025-05-31T07:48:15.473Z In(05) vmx DICT --- USER PREFERENCES C:\Users\<USER>\AppData\Roaming\VMware\preferences.ini
2025-05-31T07:48:15.473Z In(05) vmx DICT   pref.lastUpdateCheckSec = "1748677464"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.enabled = "FALSE"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.keyboardAndMouse.vmHotKey.count = "0"
2025-05-31T07:48:15.473Z In(05) vmx DICT      pref.mruVM0.filename = "D:\kali-linux-2025.1c-vmware-amd64\kali-linux-2025.1c-vmware-amd64.vmwarevm\kali-linux-2025.1c-vmware-amd64.vmx"
2025-05-31T07:48:15.473Z In(05) vmx DICT   pref.mruVM0.displayName = "kali-linux-2025.1c-vmware-amd64"
2025-05-31T07:48:15.473Z In(05) vmx DICT         pref.mruVM0.index = "0"
2025-05-31T07:48:15.473Z In(05) vmx DICT hint.loader.mitigations.wsAndFusion = "FALSE"
2025-05-31T07:48:15.473Z In(05) vmx DICT             hints.hideAll = "FALSE"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.wspro.firstRunDismissedVersion = "17.6.1"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window.count = "1"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab.count = "3"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab0.dest = ""
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab0.file = ""
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab0.type = "home"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab0.cnxType = "vmdb"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab0.focused = "FALSE"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab1.dest = ""
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab1.file = "folder:localhost"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab1.type = "folder"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab1.cnxType = "vmdb"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab1.focused = "FALSE"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab2.dest = ""
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab2.file = "D:\kali-linux-2025.1c-vmware-amd64\kali-linux-2025.1c-vmware-amd64.vmwarevm\kali-linux-2025.1c-vmware-amd64.vmx"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab2.type = "vm"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab2.cnxType = "vmdb"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tab2.focused = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.sidebar = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.sidebar.width = "200"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.statusBar = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.tabs = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar = "FALSE"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.size = "125"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.thumbnailBar.view = "same-folder"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.placement.left = "239"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.placement.top = "83"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.placement.right = "1886"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.placement.bottom = "820"
2025-05-31T07:48:15.473Z In(05) vmx DICT pref.ws.session.window0.maximized = "FALSE"
2025-05-31T07:48:15.473Z In(05) vmx DICT      pref.namedPVNs.count = "1"
2025-05-31T07:48:15.473Z In(05) vmx DICT      pref.namedPVNs0.name = "***********"
2025-05-31T07:48:15.473Z In(05) vmx DICT     pref.namedPVNs0.pvnID = "52 17 57 99 73 82 39 83-d1 39 83 aa 5e cd a0 83"
2025-05-31T07:48:15.473Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini
2025-05-31T07:48:15.473Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2025-05-31T07:48:15.473Z In(05) vmx DICT         authd.client.port = "902"
2025-05-31T07:48:15.473Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "20875"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "20875"
2025-05-31T07:48:15.473Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini
2025-05-31T07:48:15.473Z In(05) vmx DICT         authd.client.port = "902"
2025-05-31T07:48:15.473Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "20875"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "20875"
2025-05-31T07:48:15.473Z In(05) vmx DICT --- NONPERSISTENT
2025-05-31T07:48:15.473Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT             gui.available = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT --- COMMAND LINE
2025-05-31T07:48:15.473Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT             gui.available = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT --- RECORDING
2025-05-31T07:48:15.473Z In(05) vmx DICT            vmx.stdio.keep = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT             gui.available = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT --- CONFIGURATION D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmx 
2025-05-31T07:48:15.473Z In(05) vmx DICT            config.version = "8"
2025-05-31T07:48:15.473Z In(05) vmx DICT         virtualHW.version = "16"
2025-05-31T07:48:15.473Z In(05) vmx DICT        pciBridge0.present = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT        pciBridge4.present = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT     pciBridge4.virtualDev = "pcieRootPort"
2025-05-31T07:48:15.473Z In(05) vmx DICT      pciBridge4.functions = "8"
2025-05-31T07:48:15.473Z In(05) vmx DICT        pciBridge5.present = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT     pciBridge5.virtualDev = "pcieRootPort"
2025-05-31T07:48:15.473Z In(05) vmx DICT      pciBridge5.functions = "8"
2025-05-31T07:48:15.473Z In(05) vmx DICT        pciBridge6.present = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT     pciBridge6.virtualDev = "pcieRootPort"
2025-05-31T07:48:15.473Z In(05) vmx DICT      pciBridge6.functions = "8"
2025-05-31T07:48:15.473Z In(05) vmx DICT        pciBridge7.present = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT     pciBridge7.virtualDev = "pcieRootPort"
2025-05-31T07:48:15.473Z In(05) vmx DICT      pciBridge7.functions = "8"
2025-05-31T07:48:15.473Z In(05) vmx DICT             vmci0.present = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT             hpet0.present = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT                     nvram = "LouLx-Game.nvram"
2025-05-31T07:48:15.473Z In(05) vmx DICT virtualHW.productCompatibility = "hosted"
2025-05-31T07:48:15.473Z In(05) vmx DICT        powerType.powerOff = "soft"
2025-05-31T07:48:15.473Z In(05) vmx DICT         powerType.powerOn = "soft"
2025-05-31T07:48:15.473Z In(05) vmx DICT         powerType.suspend = "soft"
2025-05-31T07:48:15.473Z In(05) vmx DICT           powerType.reset = "soft"
2025-05-31T07:48:15.473Z In(05) vmx DICT               displayName = "Đại Chiến Quốc Mobile LouLx"
2025-05-31T07:48:15.473Z In(05) vmx DICT usb.vbluetooth.startConnected = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT                   guestOS = "centos7-64"
2025-05-31T07:48:15.473Z In(05) vmx DICT            tools.syncTime = "FALSE"
2025-05-31T07:48:15.473Z In(05) vmx DICT          sound.autoDetect = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT            sound.fileName = "-1"
2025-05-31T07:48:15.473Z In(05) vmx DICT                  numvcpus = "2"
2025-05-31T07:48:15.473Z In(05) vmx DICT      cpuid.coresPerSocket = "2"
2025-05-31T07:48:15.473Z In(05) vmx DICT               vcpu.hotadd = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT                   memsize = "4096"
2025-05-31T07:48:15.473Z In(05) vmx DICT                mem.hotadd = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT          scsi0.virtualDev = "lsilogic"
2025-05-31T07:48:15.473Z In(05) vmx DICT             scsi0.present = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT          scsi0:0.fileName = "LouLx-Game.vmdk"
2025-05-31T07:48:15.473Z In(05) vmx DICT           scsi0:0.present = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT         ide1:0.deviceType = "cdrom-image"
2025-05-31T07:48:15.473Z In(05) vmx DICT           ide1:0.fileName = "E:\VM\CentOS-7-x86_64-Minimal-2009.iso"
2025-05-31T07:48:15.473Z In(05) vmx DICT  ethernet0.connectionType = "nat"
2025-05-31T07:48:15.473Z In(05) vmx DICT     ethernet0.addressType = "generated"
2025-05-31T07:48:15.473Z In(05) vmx DICT      ethernet0.virtualDev = "e1000"
2025-05-31T07:48:15.473Z In(05) vmx DICT          serial0.fileType = "thinprint"
2025-05-31T07:48:15.473Z In(05) vmx DICT          serial0.fileName = "thinprint"
2025-05-31T07:48:15.473Z In(05) vmx DICT         ethernet0.present = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT        extendedConfigFile = "LouLx-Game.vmxf"
2025-05-31T07:48:15.473Z In(05) vmx DICT           floppy0.present = "FALSE"
2025-05-31T07:48:15.473Z In(05) vmx DICT                 uuid.bios = "56 4d 90 07 c8 75 9d 8b-18 cd 1f d4 62 17 98 f4"
2025-05-31T07:48:15.473Z In(05) vmx DICT             uuid.location = "56 4d 90 07 c8 75 9d 8b-18 cd 1f d4 62 17 98 f4"
2025-05-31T07:48:15.473Z In(05) vmx DICT              scsi0:0.redo = ""
2025-05-31T07:48:15.473Z In(05) vmx DICT  pciBridge0.pciSlotNumber = "17"
2025-05-31T07:48:15.473Z In(05) vmx DICT  pciBridge4.pciSlotNumber = "21"
2025-05-31T07:48:15.473Z In(05) vmx DICT  pciBridge5.pciSlotNumber = "22"
2025-05-31T07:48:15.473Z In(05) vmx DICT  pciBridge6.pciSlotNumber = "23"
2025-05-31T07:48:15.473Z In(05) vmx DICT  pciBridge7.pciSlotNumber = "24"
2025-05-31T07:48:15.473Z In(05) vmx DICT       scsi0.pciSlotNumber = "16"
2025-05-31T07:48:15.473Z In(05) vmx DICT         usb.pciSlotNumber = "-1"
2025-05-31T07:48:15.473Z In(05) vmx DICT   ethernet0.pciSlotNumber = "33"
2025-05-31T07:48:15.473Z In(05) vmx DICT       sound.pciSlotNumber = "-1"
2025-05-31T07:48:15.473Z In(05) vmx DICT        ehci.pciSlotNumber = "-1"
2025-05-31T07:48:15.473Z In(05) vmx DICT             svga.vramSize = "268435456"
2025-05-31T07:48:15.473Z In(05) vmx DICT  vmotion.checkpointFBSize = "4194304"
2025-05-31T07:48:15.473Z In(05) vmx DICT vmotion.checkpointSVGAPrimarySize = "268435456"
2025-05-31T07:48:15.473Z In(05) vmx DICT ethernet0.generatedAddress = "00:0c:29:17:98:f4"
2025-05-31T07:48:15.473Z In(05) vmx DICT ethernet0.generatedAddressOffset = "0"
2025-05-31T07:48:15.473Z In(05) vmx DICT                  vmci0.id = "-1126817593"
2025-05-31T07:48:15.473Z In(05) vmx DICT    monitor.phys_bits_used = "43"
2025-05-31T07:48:15.473Z In(05) vmx DICT             cleanShutdown = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT              softPowerOff = "FALSE"
2025-05-31T07:48:15.473Z In(05) vmx DICT             usb:0.present = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT          usb:0.deviceType = "hid"
2025-05-31T07:48:15.473Z In(05) vmx DICT                usb:0.port = "0"
2025-05-31T07:48:15.473Z In(05) vmx DICT              usb:0.parent = "-1"
2025-05-31T07:48:15.473Z In(05) vmx DICT               usb:1.speed = "2"
2025-05-31T07:48:15.473Z In(05) vmx DICT             usb:1.present = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT          usb:1.deviceType = "hub"
2025-05-31T07:48:15.473Z In(05) vmx DICT                usb:1.port = "1"
2025-05-31T07:48:15.473Z In(05) vmx DICT              usb:1.parent = "-1"
2025-05-31T07:48:15.473Z In(05) vmx DICT svga.guestBackedPrimaryAware = "TRUE"
2025-05-31T07:48:15.473Z In(05) vmx DICT       tools.remindInstall = "FALSE"
2025-05-31T07:48:15.473Z In(05) vmx DICT                annotation = <not printed>
2025-05-31T07:48:15.473Z In(05) vmx DICT         vmxstats.filename = "LouLx-Game.scoreboard"
2025-05-31T07:48:15.473Z In(05) vmx DICT --- USER DEFAULTS C:\Users\<USER>\AppData\Roaming\VMware\config.ini 
2025-05-31T07:48:15.473Z In(05) vmx DICT --- HOST DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2025-05-31T07:48:15.473Z In(05) vmx DICT         authd.client.port = "902"
2025-05-31T07:48:15.473Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "20875"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "20875"
2025-05-31T07:48:15.473Z In(05) vmx DICT --- SITE DEFAULTS C:\ProgramData\VMware\VMware Workstation\config.ini 
2025-05-31T07:48:15.473Z In(05) vmx DICT         authd.client.port = "902"
2025-05-31T07:48:15.473Z In(05) vmx DICT           authd.proxy.nfc = "vmware-hostd:ha-nfc"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled = "yes"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.autoSoftwareUpdateEnabled.epoch = "20875"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.componentDownloadEnabled = "yes"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.dataCollectionEnabled = "yes"
2025-05-31T07:48:15.473Z In(05) vmx DICT installerDefaults.dataCollectionEnabled.epoch = "20875"
2025-05-31T07:48:15.473Z In(05) vmx DICT --- GLOBAL SETTINGS C:\ProgramData\VMware\VMware Workstation\settings.ini 
2025-05-31T07:48:15.473Z In(05) vmx DICT          printers.enabled = "FALSE"
2025-05-31T07:48:15.474Z In(05) vmx Powering on guestOS 'centos7-64' using the configuration for 'centos7-64'.
2025-05-31T07:48:15.474Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2025-05-31T07:48:15.474Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2025-05-31T07:48:15.474Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-05-31T07:48:15.474Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'centos7-64' guest.
2025-05-31T07:48:15.474Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-31T07:48:15.474Z In(05) vmx DEVSWAP: GuestOS does not require LSI adapter swap.
2025-05-31T07:48:15.475Z In(05) vmx Monitor Mode: ULM
2025-05-31T07:48:15.475Z In(05) vmx MsgHint: msg.loader.mitigations.wsAndFusion
2025-05-31T07:48:15.475Z In(05)+ vmx You are running this virtual machine with side channel mitigations enabled. Side channel mitigations provide enhanced security but also lower performance.
2025-05-31T07:48:15.475Z In(05)+ vmx 
2025-05-31T07:48:15.475Z In(05)+ vmx To disable mitigations, change the side channel mitigations setting in the advanced panel of the virtual machine settings. Refer to VMware KB article 79832 at https://kb.vmware.com/s/article/79832 for more details.
2025-05-31T07:48:15.475Z In(05)+ vmx ---------------------------------------
2025-05-31T07:48:15.485Z In(05) vmx OvhdMem_PowerOn: initial admission: paged   510490 nonpaged     7545 anonymous     8046
2025-05-31T07:48:15.485Z In(05) vmx VMMEM: Initial Reservation: 2055MB (MainMem=4096MB)
2025-05-31T07:48:15.485Z In(05) vmx numa: Hot-add is enabled and vNUMA hot-add is disabled, forcing UMA.
2025-05-31T07:48:15.486Z In(05) vmx llc: maximum vcpus per LLC: 1
2025-05-31T07:48:15.486Z In(05) vmx llc: vLLC size: 2
2025-05-31T07:48:15.487Z In(05) vmx MemSched_PowerOn: balloon minGuestSize 104857 (80% of min required size 131072)
2025-05-31T07:48:15.487Z In(05) vmx MemSched: reserved mem (in MB) min 128 max 13601 recommended 13601
2025-05-31T07:48:15.487Z In(05) vmx MemSched: pg 510490 np 7545 anon 8046 mem 1048576
2025-05-31T07:48:15.518Z In(05) vmx MemSched: numvm 1 locked pages: num 0 max 2804479
2025-05-31T07:48:15.518Z In(05) vmx MemSched: locked Page Limit: host 2812671 config 3481856
2025-05-31T07:48:15.518Z In(05) vmx MemSched: minmempct 50 minalloc 0 admitted 1
2025-05-31T07:48:15.519Z In(05) PowerNotifyThread VTHREAD 24208 "PowerNotifyThread"
2025-05-31T07:48:15.519Z In(05) PowerNotifyThread PowerNotify thread is alive.
2025-05-31T07:48:15.519Z In(05) vmx LICENSE: Running unlicensed VMX (VMware Workstation)
2025-05-31T07:48:15.519Z In(05) vthread-16172 VTHREAD 16172 "vthread-16172"
2025-05-31T07:48:15.519Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmpl", ...) failed, error: 2
2025-05-31T07:48:15.519Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2025-05-31T07:48:15.519Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmpl", ...) failed, error: 2
2025-05-31T07:48:15.519Z In(05) vmx PolicyVMXFindPolicyKey: policy file does not exist.
2025-05-31T07:48:15.520Z In(05) vmx Host PA size: 39 bits. Guest PA size: 43 bits.
2025-05-31T07:48:15.521Z In(05) vmx ToolsISO: Refreshing imageName for 'centos7-64' (refreshCount=1, lastCount=1).
2025-05-31T07:48:15.521Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2025-05-31T07:48:15.521Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2025-05-31T07:48:15.521Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-05-31T07:48:15.521Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'centos7-64' guest.
2025-05-31T07:48:15.521Z In(05) deviceThread VTHREAD 3888 "deviceThread"
2025-05-31T07:48:15.521Z In(05) deviceThread Device thread is alive
2025-05-31T07:48:15.522Z In(05) vmx KHZEstimate 3187201
2025-05-31T07:48:15.522Z In(05) vmx MHZEstimate 3187
2025-05-31T07:48:15.522Z In(05) vmx NumVCPUs 2
2025-05-31T07:48:15.522Z In(05) vmx AIOGNRC: numThreads=17 ide=0, scsi=1, passthru=0
2025-05-31T07:48:15.522Z In(05) vmx WORKER: Creating new group with maxThreads=17 (17)
2025-05-31T07:48:15.524Z In(05) vmx WORKER: Creating new group with maxThreads=1 (18)
2025-05-31T07:48:15.524Z In(05) vmx MainMem: CPT Host WZ=0 PF=4096 D=0
2025-05-31T07:48:15.524Z In(05) vmx MainMem: CPT PLS=1 PLR=1 BS=1 BlkP=32 Mult=4 W=50
2025-05-31T07:48:15.524Z In(05) vmx MainMem: Opened paging file, 'D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\564d9007-c875-9d8b-18cd-1fd4621798f4.vmem'.
2025-05-31T07:48:15.524Z In(05) vmx MStat: Creating Stat vm.uptime
2025-05-31T07:48:15.524Z In(05) vmx MStat: Creating Stat vm.suspendTime
2025-05-31T07:48:15.524Z In(05) vmx MStat: Creating Stat vm.powerOnTimeStamp
2025-05-31T07:48:15.526Z In(05) aioCompletion VTHREAD 11076 "aioCompletion"
2025-05-31T07:48:15.526Z In(05) vmx VMXAIOMGR: Using: simple=Compl
2025-05-31T07:48:15.527Z In(05) vmx WORKER: Creating new group with maxThreads=1 (19)
2025-05-31T07:48:15.529Z In(05) vmx WORKER: Creating new group with maxThreads=1 (20)
2025-05-31T07:48:15.529Z In(05) vmx WORKER: Creating new group with maxThreads=14 (34)
2025-05-31T07:48:15.529Z In(05) vmx FeatureCompat: No VM masks.
2025-05-31T07:48:15.529Z In(05) vmx TimeTracker host to guest rate conversion 420271966 @ 3187200940Hz -> 0 @ 3187200940Hz
2025-05-31T07:48:15.529Z In(05) vmx TimeTracker host to guest rate conversion ((x * 2147483648) >> 31) + -420271966
2025-05-31T07:48:15.529Z In(05) vmx Disabling TSC scaling since host does not support it.
2025-05-31T07:48:15.529Z In(05) vmx TSC offsetting enabled.
2025-05-31T07:48:15.529Z In(05) vmx timeTracker.globalProgressMaxAllowanceMS: 2000
2025-05-31T07:48:15.529Z In(05) vmx timeTracker.globalProgressToAllowanceNS: 1000
2025-05-31T07:48:15.529Z In(05) vmx MKS PowerOn
2025-05-31T07:48:15.530Z In(05) mks VTHREAD 25340 "mks"
2025-05-31T07:48:15.530Z In(05) mks MKS thread is alive
2025-05-31T07:48:15.531Z In(05) svga VTHREAD 26216 "svga"
2025-05-31T07:48:15.531Z In(05) svga SVGA thread is alive
2025-05-31T07:48:15.531Z In(05) mks MKS: SSE2=1, SSSE3=1, SSE4_1=1
2025-05-31T07:48:15.531Z In(05) mouse VTHREAD 12364 "mouse"
2025-05-31T07:48:15.531Z In(05) mks MKS-HookKeyboard: RegQueryValueEx(LowLevelHooksTimeout) failed: The system cannot find the file specified (2)
2025-05-31T07:48:15.531Z In(05) kbh VTHREAD 22460 "kbh"
2025-05-31T07:48:15.532Z In(05) mks MKSWin32: Registering top level window (0x50a7e) to receive session change notification.
2025-05-31T07:48:15.534Z In(05) mks MKSWin32: Disabled power throttling.
2025-05-31T07:48:15.534Z In(05) mks Current Display Settings:
2025-05-31T07:48:15.534Z In(05) mks    Display: 0 size: 3840x2400  position: (0, 0) name: \\.\DISPLAY1  
2025-05-31T07:48:15.534Z In(05) mks MKSWin32: MIL: 0x4000
2025-05-31T07:48:15.534Z In(05) mks MKSRenderMain: PowerOn allowed BasicOps 
2025-05-31T07:48:15.534Z In(05) mks MKSRenderMain: ISB enabled by config
2025-05-31T07:48:15.534Z In(05) mks MKSRenderMain: Collecting RenderOps caps from BasicOps
2025-05-31T07:48:15.534Z In(05) mks MKSRenderMain: Starting BasicOps
2025-05-31T07:48:15.534Z In(05) mks MKSRenderMain: Started BasicOps
2025-05-31T07:48:15.534Z In(05) mks MKSRenderMain: Found Full Renderer: MKSBasicOps
2025-05-31T07:48:15.534Z In(05) mks MKSRenderMain: maxTextureSize=32768
2025-05-31T07:48:15.535Z In(05) mks KHBKL: Unable to parse keystring at: ''
2025-05-31T07:48:15.535Z In(05) mks MKSRemoteMgr: Set default display name: Đại Chiến Quốc Mobile LouLx
2025-05-31T07:48:15.535Z In(05) mks MKSRemoteMgr: Loading VNC Configuration from VM config file
2025-05-31T07:48:15.535Z In(05) mks MKSRemoteMgr: Using default VNC keymap table "us"
2025-05-31T07:48:15.535Z In(05) vmx VLANCE: send cluster threshold is 80, size = 2 recalcInterval is 20000 us
2025-05-31T07:48:15.535Z In(05) vmx VMXNET: send cluster threshold is 80, size = 2 recalcInterval is 20000 ticks, dontClusterSize is 128
2025-05-31T07:48:15.535Z In(05) vmx Chipset version: 0x17
2025-05-31T07:48:15.536Z In(05) vmx SOUNDLIB: Creating the Wave sound backend.
2025-05-31T07:48:15.540Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "-1"
2025-05-31T07:48:15.540Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "-1"
2025-05-31T07:48:15.540Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "-1"
2025-05-31T07:48:15.540Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "-1"
2025-05-31T07:48:15.540Z No(00) vmx ConfigDB: Setting pciBridge4.pciSlotNumber = "21"
2025-05-31T07:48:15.540Z No(00) vmx ConfigDB: Setting pciBridge5.pciSlotNumber = "22"
2025-05-31T07:48:15.540Z No(00) vmx ConfigDB: Setting pciBridge6.pciSlotNumber = "23"
2025-05-31T07:48:15.540Z No(00) vmx ConfigDB: Setting pciBridge7.pciSlotNumber = "24"
2025-05-31T07:48:15.548Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation begins.
2025-05-31T07:48:15.548Z In(05) vmx MigrateBusMemPrealloc: BusMem preallocation completes.
2025-05-31T07:48:15.549Z No(00) vmx ConfigDB: Setting scsi0:0.redo = ""
2025-05-31T07:48:15.549Z In(05) vmx DISK: OPEN scsi0:0 'D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmdk' persistent R[]
2025-05-31T07:48:15.557Z In(05) vmx DiskGetGeometry: Reading of disk partition table
2025-05-31T07:48:15.557Z In(05) vmx DISK: Disk 'D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmdk' has UUID '60 00 c2 9b 7a 6f 2b 65-fe bb e5 fb 15 2c 5c cd'
2025-05-31T07:48:15.557Z In(05) vmx DISK: OPEN 'D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmdk' Geo (7832/255/63) BIOS Geo (7832/255/63)
2025-05-31T07:48:15.571Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.572Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.573Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.573Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.574Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.574Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.576Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.576Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.577Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.577Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.578Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.578Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.581Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.581Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.582Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.583Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.584Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.584Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.585Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.585Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.586Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.586Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.587Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.587Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.589Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.589Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.590Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.590Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.591Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.591Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.592Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsATASSDDevice: Failed to send ATA IDENTIFY command to the target device.
2025-05-31T07:48:15.592Z In(05) vmx DISKUTILWIN32: DiskUtilW32IsScsiSSDDevice: Reported rotation rate = 1
2025-05-31T07:48:15.592Z In(05) vmx DISK: DiskConfigureVirtualSSD:  Disk 'scsi0:0' identified as Virtual SSD device.
2025-05-31T07:48:15.592Z In(05) vmx DISK: Opening disks took 44 ms.
2025-05-31T07:48:15.593Z Wa(03) vmx USB: No USB controllers found.
2025-05-31T07:48:15.593Z In(05) vmx SCSI: scsi0: intr coalescing: on period=50msec cifTh=4 iopsTh=2000 hlt=0
2025-05-31T07:48:15.593Z In(05) vmx SCSI0: UNTAGGED commands will be converted to ORDER tags.
2025-05-31T07:48:15.593Z In(05) vmx SCSI DEVICE (scsi0:0): Computed value of scsi0:0.useBounceBuffers: default
2025-05-31T07:48:15.593Z In(05) vmx DISKUTIL: scsi0:0 : capacity=125829120 logical sector size=512
2025-05-31T07:48:15.593Z In(05) vmx DISKUTIL: scsi0:0 : geometry=7832/255/63
2025-05-31T07:48:15.593Z In(05) vmx SVGA-GFB: Config settings: autodetect=1, numDisplays=1, maxWidth=2560, maxHeight=1600
2025-05-31T07:48:15.593Z In(05) vmx SVGA-GFB: Desired maximum display topology: wh(6688, 5016)
2025-05-31T07:48:15.593Z In(05) vmx SVGA-GFB: Autodetected target gfbSize = 268435456
2025-05-31T07:48:15.593Z In(05) vmx SVGA-GFB: Using Initial       gfbSize = 4194304
2025-05-31T07:48:15.593Z In(05) vmx SVGA-GFB: MaxPrimaryMem      register = 268435456
2025-05-31T07:48:15.593Z In(05) vmx SVGA-GFB: Truncated maximum resolution for register modes to VRAM size: VRAM=4194304 bytes, max wh(1176, 885)
2025-05-31T07:48:15.593Z In(05) vmx SVGA-GFB: Max wh(1176, 885), number of displays: 10
2025-05-31T07:48:15.593Z In(05) vmx SVGA-GFB: Allocated gfbSize=4194304
2025-05-31T07:48:15.593Z No(00) vmx ConfigDB: Setting vmotion.checkpointFBSize = "4194304"
2025-05-31T07:48:15.593Z No(00) vmx ConfigDB: Setting vmotion.checkpointSVGAPrimarySize = "268435456"
2025-05-31T07:48:15.593Z In(05) vmx SVGA: SVGA DeviceLabel: svga2
2025-05-31T07:48:15.593Z In(05) vmx SVGA: mobMaxSize=134217728
2025-05-31T07:48:15.593Z In(05) vmx SVGA: graphicsMemoryKB=262144
2025-05-31T07:48:15.593Z In(05) vmx SVGA: FIFO capabilities 0x0000077f
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.supports3D bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.baseCapsLevel num 11
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxPointSize num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureSize num 32768
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxVolumeExtent num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxTextureAnisotropy num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.lineStipple bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxMaxConstantBuffers num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.dxProvokingVertex bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm41 bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample2x bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample4x bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.msFullQuality bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicOps bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.bc67 num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.sm5 bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.multisample8x bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.logicBlendOps bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.maxForcedSampleCount num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (before clamping) svga.gl43 bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.supports3D bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.baseCapsLevel num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxPointSize num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureSize num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxVolumeExtent num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxTextureAnisotropy num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.lineStipple bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxMaxConstantBuffers num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.dxProvokingVertex bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm41 bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample2x bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample4x bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.msFullQuality bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicOps bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.bc67 num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.sm5 bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.multisample8x bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.logicBlendOps bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.maxForcedSampleCount num 0
2025-05-31T07:48:15.594Z In(05) vmx SVGAFeature renderer (after  clamping) svga.gl43 bool 0
2025-05-31T07:48:15.594Z In(05) vmx SVGA3dClamp: Renderer Provides     BC67Level:     0 (    0,     0)
2025-05-31T07:48:15.594Z In(05) vmx SVGA3dClamp: Renderer Provides BaseCapsLevel:     0 (    0,     0)
2025-05-31T07:48:15.594Z In(05) vmx SVGA3dClamp: Renderer Provides    ClampLevel:     0 (    0,     0)
2025-05-31T07:48:15.594Z In(05) vmx SVGA3dCaps: host, at power on (3d disabled)
2025-05-31T07:48:15.594Z In(05) vmx   cap[ 19]: 0x00002000 (MAX_TEXTURE_WIDTH)
2025-05-31T07:48:15.594Z In(05) vmx   cap[ 20]: 0x00002000 (MAX_TEXTURE_HEIGHT)
2025-05-31T07:48:15.594Z In(05) vmx   cap[ 93]: 0x00000001 (TS_COLOR_KEY)
2025-05-31T07:48:15.594Z In(05) vmx SVGA3dClamp:     Host Provides     BC67Level:     0 (    0,     0)
2025-05-31T07:48:15.594Z In(05) vmx SVGA3dClamp:     Host Provides BaseCapsLevel:     0 (    0,     4)
2025-05-31T07:48:15.594Z In(05) vmx SVGA3dClamp:     Host Provides    ClampLevel:     0 (    0,     0)
2025-05-31T07:48:15.594Z In(05) vmx SVGA3dCaps: Disabling 3d support
2025-05-31T07:48:15.594Z In(05) vmx SVGA3dCaps: guest, compatibility level: 8
2025-05-31T07:48:15.594Z In(05) vmx   cap[ 19]: 0x00002000 (MAX_TEXTURE_WIDTH)
2025-05-31T07:48:15.594Z In(05) vmx   cap[ 20]: 0x00002000 (MAX_TEXTURE_HEIGHT)
2025-05-31T07:48:15.594Z In(05) vmx SVGA3dClamp:    Guest Requires     BC67Level:     0 (    0,     0)
2025-05-31T07:48:15.594Z In(05) vmx SVGA3dClamp:    Guest Requires BaseCapsLevel:     0 (    0,     0)
2025-05-31T07:48:15.594Z In(05) vmx SVGA3dClamp:    Guest Requires    ClampLevel:     0 (    0,     0)
2025-05-31T07:48:15.599Z In(05) vmx Ethernet0 MAC Address: 00:0c:29:17:98:f4
2025-05-31T07:48:15.600Z No(00) vmx ConfigDB: Setting vmci0.id = "-1126817593"
2025-05-31T07:48:15.606Z In(05) vmx WORKER: Creating new group with maxThreads=1 (35)
2025-05-31T07:48:15.606Z In(05) vmx DISKUTIL: (null) : max toolsVersion = 0, type = 0
2025-05-31T07:48:15.606Z In(05) vmx DISKUTIL: Offline toolsVersion = 0, type = 0
2025-05-31T07:48:15.606Z In(05) vmx TOOLS setting legacy tools version to '0' type 0, manifest status is 9
2025-05-31T07:48:15.606Z In(05) vmx ToolsISO: Refreshing imageName for 'centos7-64' (refreshCount=1, lastCount=1).
2025-05-31T07:48:15.606Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2025-05-31T07:48:15.606Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2025-05-31T07:48:15.607Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-05-31T07:48:15.607Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'centos7-64' guest.
2025-05-31T07:48:15.607Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Program Files (x86)\VMware\VMware Workstation\linux.iso", ...) failed, error: 2
2025-05-31T07:48:15.607Z In(05) vmx TOOLS updated cached value for isoImageExists to 0.
2025-05-31T07:48:15.607Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'notAvailable', 'noTools', install impossible
2025-05-31T07:48:15.607Z In(05) vmx ToolsISO: Refreshing imageName for 'centos7-64' (refreshCount=1, lastCount=1).
2025-05-31T07:48:15.607Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2025-05-31T07:48:15.607Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2025-05-31T07:48:15.607Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-05-31T07:48:15.607Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'centos7-64' guest.
2025-05-31T07:48:15.607Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Program Files (x86)\VMware\VMware Workstation\linux.iso", ...) failed, error: 2
2025-05-31T07:48:15.607Z In(05) vmx TOOLS updated cached value for isoImageExists to 0.
2025-05-31T07:48:15.607Z In(05) vmx VMXVmdb_SetToolsVersionStatus: status value set to 'notAvailable', 'noTools', install impossible
2025-05-31T07:48:15.607Z In(05) vmx Tools: sending 'OS_PowerOn' (state = 3) state change request
2025-05-31T07:48:15.607Z In(05) vmx Tools: Delaying state change request to state 3.
2025-05-31T07:48:15.607Z In(05) vmx TOOLS INSTALL initializing state to IDLE on power on.
2025-05-31T07:48:15.607Z In(05) vmx TOOLS INSTALL updating Rpc handlers registration.
2025-05-31T07:48:15.607Z In(05) vmx TOOLS INSTALL register RPC: upgrader.setGuestFileRoot
2025-05-31T07:48:15.607Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.is_image_inserted
2025-05-31T07:48:15.607Z In(05) vmx TOOLS INSTALL register RPC: toolinstall.installerActive
2025-05-31T07:48:15.607Z In(05) vmx TOOLS INSTALL register RPC: guest.upgrader_send_cmd_line_args
2025-05-31T07:48:15.607Z In(05) vmx P9FS_PowerOn: 9PFS server is not enabled.
2025-05-31T07:48:15.607Z In(05) vmx HgfsServerManagerVigorInit: Initialize: dev api
2025-05-31T07:48:15.608Z In(05) vmx MKSBackdoor: Copy/paste enabled = 1
2025-05-31T07:48:15.608Z In(05) vmx DEPLOYPKG: No pending deploy package name set
2025-05-31T07:48:15.608Z In(05) vmx DEPLOYPKG: ToolsDeployPkgPublishState: state=0, code=0, message=(null)
2025-05-31T07:48:15.644Z No(00) vmx ConfigDB: Setting monitor.phys_bits_used = "43"
2025-05-31T07:48:15.644Z In(05) vmx Full guest CPUID with differences from hostCPUID highlighted.
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest vendor: GenuineIntel
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest family: 0x6 model: 0xba stepping: 0x2
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest codename: Raptor Lake H/P/PX/U
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest name: 13th Gen Intel(R) CoreT i5-13500H
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID       level eaxIn, ecxIn:        eax        ebx        ecx        edx
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000000,  0: 0x00000020 0x756e6547 0x6c65746e 0x49656e69
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000001,  0: 0x000b06a2 0x00020800 0xf6fa3203 0x1f8bfbff
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 00000001,  0: 0x000b06a2 0x00400800 0xfffaf38b 0xbfcbfbff
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000002,  0: 0x00feff01 0x000000f0 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000004,  0: 0x04000121 0x02c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 00000004,  0: 0x7c004121 0x02c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000004,  1: 0x04000122 0x01c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 00000004,  1: 0x7c004122 0x01c0003f 0x0000003f 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000004,  2: 0x04000143 0x0240003f 0x000007ff 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 00000004,  2: 0x7c01c143 0x0240003f 0x000007ff 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000004,  3: 0x04004163 0x02c0003f 0x00005fff 0x00000004
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 00000004,  3: 0x7c0fc163 0x02c0003f 0x00005fff 0x00000004
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000004,  4: 0x04000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 00000004,  4: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000006,  0: 0x00000004 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 00000006,  0: 0x009f8ff3 0x00000002 0x00000409 0x00000003
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000007,  0: 0x00000000 0x219c27ab 0x00000000 0xbc000400
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 00000007,  0: 0x00000002 0x239c27a9 0x184007a4 0xbc18c410
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000007,  1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 00000007,  1: 0x00400810 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000007,  2: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 00000007,  2: 0x00000000 0x00000000 0x00000000 0x00000011
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 0000000a,  0: 0x07300601 0x000000ff 0x00000007 0x00008000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 0000000a,  0: 0x07300605 0x00000000 0x00000007 0x00008603
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 0000000b,  0: 0x00000000 0x00000001 0x00000100 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 0000000b,  0: 0x00000001 0x00000002 0x00000100 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 0000000b,  1: 0x00000001 0x00000002 0x00000201 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 0000000b,  1: 0x00000006 0x00000010 0x00000201 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 0000000d,  0: 0x00000007 0x00000340 0x00000340 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 0000000d,  1: 0x0000000b 0x00000350 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 0000000d,  1: 0x0000000f 0x00000350 0x00001800 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 0000000d,  2: 0x00000100 0x00000240 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000014,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 00000014,  0: 0x00000001 0x0000005f 0x00000007 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000014,  1: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 00000014,  1: 0x02490002 0x003f003f 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 00000015,  0: 0x00000000 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 00000015,  0: 0x00000002 0x000000a6 0x0249f000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 40000000,  0: 0x40000010 0x61774d56 0x4d566572 0x65726177
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 40000000,  0: 0x4000000c 0x7263694d 0x666f736f 0x76482074
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 40000010,  0: 0x0030a201 0x000101d0 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 80000000,  0: 0x80000008 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 80000001,  0: 0x00000000 0x00000000 0x00000121 0x2c100800
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 80000002,  0: 0x68743331 0x6e654720 0x746e4920 0x52286c65
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 80000003,  0: 0x6f432029 0x20546572 0x312d3569 0x30303533
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 80000004,  0: 0x00000048 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 80000006,  0: 0x00000000 0x00000000 0x05007040 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 80000007,  0: 0x00000000 0x00000000 0x00000000 0x00000100
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID guest level 80000008,  0: 0x0000302b 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx guest vs. host CPUID *host level 80000008,  0: 0x00003027 0x00000000 0x00000000 0x00000000
2025-05-31T07:48:15.644Z In(05) vmx Minimum ucode level: 0x0000410c
2025-05-31T07:48:15.644Z In(05) vmx VPMC: events will use hybrid freeze.
2025-05-31T07:48:15.644Z In(05) vmx VPMC: gen counters: num 6 mask 0xffffffffffff
2025-05-31T07:48:15.644Z In(05) vmx VPMC: fix counters: num 0 mask 0; version 1
2025-05-31T07:48:15.644Z In(05) vmx VPMC: hardware counters: 0
2025-05-31T07:48:15.644Z In(05) vmx VPMC: perf capabilities: 0x2000
2025-05-31T07:48:15.644Z In(05) vmx Guest MSR IA32_ARCH_CAPABILITIES 0x10a = 0x2f
2025-05-31T07:48:15.644Z In(05) vmx SVGA-PCI: BAR gfbSize=134217728, fifoSize=8388608
2025-05-31T07:48:15.644Z In(05) vmx SVGA: SVGA_REG_MEMORY_SIZE=4194304
2025-05-31T07:48:15.644Z In(05) vmx SVGA: SVGA_REG_VRAM_SIZE=4194304
2025-05-31T07:48:15.644Z In(05) vmx SVGA: Final Device caps : 0xfdff83e2
2025-05-31T07:48:15.644Z In(05) vmx SVGA: Final Device caps2: 0x00000007
2025-05-31T07:48:15.644Z In(05) vmx PStrIntern expansion: nBkts=256
2025-05-31T07:48:15.645Z In(05) vmx FeatureCompat: Capabilities:
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.sse3 = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.pclmulqdq = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.mwait = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.ssse3 = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.fma = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.cmpxchg16b = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.pcid = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.sse41 = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.sse42 = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.movbe = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.popcnt = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.aes = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.xsave = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.avx = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.f16c = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.rdrand = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.ds = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.ss = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.fsgsbase = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.bmi1 = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.avx2 = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.smep = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.bmi2 = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.enfstrg = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.invpcid = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.rdseed = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.adx = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.smap = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.clflushopt = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.clwb = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.sha = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.umip = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.gfni = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.vaes = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.vpclmulqdq = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.rdpid = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.movdiri = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.movdir64b = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.fast_short_repmov = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.mdclear = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.serialize = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.cet_ibt = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.stibp = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.fcmd = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.ssbd = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.avx_vnni = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.fast_short_stosb = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.psfd = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.xcr0_master_sse = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.xcr0_master_ymm_h = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.xcr0_master_bndregs = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.xcr0_master_bndcsr = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.xsaveopt = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.xsavec = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.xgetbv_ecx1 = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.xsaves = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.lahf64 = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.abm = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.3dnprefetch = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.nx = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.pdpe1gb = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.rdtscp = 1
2025-05-31T07:48:15.645Z In(05) vmx Capability Found: cpuid.lm = 1
2025-05-31T07:48:15.646Z In(05) vmx Capability Found: cpuid.intel = 1
2025-05-31T07:48:15.646Z In(05) vmx Capability Found: cpuid.ibrs = 1
2025-05-31T07:48:15.646Z In(05) vmx Capability Found: cpuid.ibpb = 1
2025-05-31T07:48:15.646Z In(05) vmx Capability Found: misc.cpuidfaulting = 1
2025-05-31T07:48:15.646Z In(05) vmx Capability Found: misc.rdcl_no = 1
2025-05-31T07:48:15.646Z In(05) vmx Capability Found: misc.ibrs_all = 1
2025-05-31T07:48:15.646Z In(05) vmx Capability Found: misc.mds_no = 1
2025-05-31T07:48:15.646Z In(05) vmx FeatureCompat: Requirements:
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.sse3 - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.pclmulqdq - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.ssse3 - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.fma - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.cmpxchg16b - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.pcid - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.sse41 - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.sse42 - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.movbe - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.popcnt - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.aes - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.xsave - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.avx - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.f16c - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.rdrand - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.ss - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.fsgsbase - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.bmi1 - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.avx2 - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.smep - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.bmi2 - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.enfstrg - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.invpcid - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.rdseed - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.adx - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.smap - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.clflushopt - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.clwb - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.sha - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.mdclear - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.stibp - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.fcmd - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.ssbd - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.xcr0_master_sse - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.xcr0_master_ymm_h - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.xsaveopt - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.xsavec - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.xsaves - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.lahf64 - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.abm - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.3dnprefetch - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.nx - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.pdpe1gb - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.rdtscp - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.lm - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.intel - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.ibrs - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: cpuid.ibpb - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: misc.rdcl_no - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: misc.ibrs_all - Bool:Min:1
2025-05-31T07:48:15.646Z In(05) vmx VM Features Required: misc.mds_no - Bool:Min:1
2025-05-31T07:48:15.708Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '1'
2025-05-31T07:48:15.709Z In(05) vmx 
2025-05-31T07:48:15.709Z In(05)+ vmx OvhdMem: Static (Power On) Overheads
2025-05-31T07:48:15.709Z In(05) vmx                                                       reserved      |          used
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  1048576 1048576      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7936   7936      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem Total excluded                      :  1073920 1073920      - |      -      -      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem Actual maximum                      :         1073920        |             -
2025-05-31T07:48:15.709Z In(05)+ vmx 
2025-05-31T07:48:15.709Z In(05) vmx                                                       reserved      |          used
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       4      4      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       2      2      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :   98304  98304      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :       6      6      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :     130    130      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :    8704   8704      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      49     49      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :     678    678      - |    492    492      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :   65537  65537      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   35840  35840      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem Total paged                         :  510490 510490      - |    502    502      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem Actual maximum                      :         510490        |        510490
2025-05-31T07:48:15.709Z In(05)+ vmx 
2025-05-31T07:48:15.709Z In(05) vmx                                                       reserved      |          used
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :       8      8      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      37     37      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_PFrame                     :       0   3266      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_BusMemFrame                :    1141   2241      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       2      2      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_SMM                        :      63     63      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :    1024   1024      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       2      2      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       2      2      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_LBR                        :       2      2      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      53     53      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem Total nonpaged                      :    2731   7545      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem Actual maximum                      :           2731        |          7545
2025-05-31T07:48:15.709Z In(05)+ vmx 
2025-05-31T07:48:15.709Z In(05) vmx                                                       reserved      |          used
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     196    196      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    1090   1147      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      16     16      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       8      8      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       2      2      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      80     80      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_TC                          :    1026   1026      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       6      6      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       9      9      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       4      4      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_HV                          :       2      2      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       2      2      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_VHV                         :       6      6      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_Numa                        :      30     30      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      58     58      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2300   2300      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     463    463      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    2310   2310      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     266    266      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       7      7      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem Total anonymous                     :    7989   8046      - |      0      0      -
2025-05-31T07:48:15.709Z In(05) vmx OvhdMem Actual maximum                      :           7989        |          8046
2025-05-31T07:48:15.709Z In(05)+ vmx 
2025-05-31T07:48:15.709Z In(05) vmx VMMEM: Precise Reservation: 2035MB (MainMem=4096MB)
2025-05-31T07:48:15.709Z In(05) vmx Vix: [mainDispatch.c:1059]: VMAutomation_PowerOn. Powering on.
2025-05-31T07:48:15.710Z In(05) vmx VMX_PowerOn: ModuleTable_PowerOn = 1
2025-05-31T07:48:15.710Z No(00) vmx ConfigDB: Setting cleanShutdown = "FALSE"
2025-05-31T07:48:15.710Z No(00) vmx ConfigDB: Setting softPowerOff = "FALSE"
2025-05-31T07:48:15.714Z In(05) vcpu-0 VTHREAD 1936 "vcpu-0"
2025-05-31T07:48:15.714Z In(05) vcpu-0 CPU reset: hard (mode Emulation)
2025-05-31T07:48:15.715Z In(05) vcpu-1 VTHREAD 22812 "vcpu-1"
2025-05-31T07:48:15.715Z In(05) vcpu-1 CPU reset: hard (mode Emulation)
2025-05-31T07:48:15.715Z In(05) svga MKSThread: SVGA thread is entering the main loop
2025-05-31T07:48:15.715Z In(05) vcpu-0 GuestRpc: Successfully created RPCI listening socket.
2025-05-31T07:48:15.715Z In(05) vcpu-0 GuestRpc: Using vsocket for TCLO messaging is disabled.
2025-05-31T07:48:15.716Z In(05) vcpu-0 memoryHotplug: Node 0: Present: 4095 MB (100 %) Size:65535 MB (100 %)
2025-05-31T07:48:15.716Z In(05) vcpu-0 PIIX4: PM Resuming from suspend type 0x0, chipset.onlineStandby 0
2025-05-31T07:48:15.716Z In(05) vcpu-0 VNET: 'ethernet0' enable link state propagation, lsp.state = 5
2025-05-31T07:48:15.716Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'ethernet0' lsp.state = 4
2025-05-31T07:48:15.717Z In(05) vcpu-0 VNET: MACVNetConnectToNetwork 'Ethernet0' notify available.
2025-05-31T07:48:15.717Z In(05) vcpu-0 HGFSPublish: publishing 0 shares
2025-05-31T07:48:15.718Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.accelerometer"
2025-05-31T07:48:15.718Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.ambientLight"
2025-05-31T07:48:15.718Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.compass"
2025-05-31T07:48:15.718Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.gyrometer"
2025-05-31T07:48:15.718Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.inclinometer"
2025-05-31T07:48:15.718Z No(00) vcpu-0 ConfigDB: Unsetting "sensor.orientation"
2025-05-31T07:48:15.719Z In(05) vcpu-0 Win32U_GetFileAttributes: GetFileAttributesExW("D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmpl", ...) failed, error: 2
2025-05-31T07:48:15.719Z In(05) vcpu-0 PolicyVMXFindPolicyKey: policy file does not exist.
2025-05-31T07:48:15.720Z In(05) vcpu-0 DEVSWAP: GuestOS does not require LSI adapter swap.
2025-05-31T07:48:15.721Z No(00) vcpu-0 Metrics lastUpdate (s): 11259
2025-05-31T07:48:15.721Z In(05) vcpu-0 Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1872, success=1 additionalError=0
2025-05-31T07:48:15.721Z In(05) vcpu-0 Vix: [mainDispatch.c:4130]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=0, err=0).
2025-05-31T07:48:15.721Z In(05) vcpu-0 Vix: [mainDispatch.c:4130]: VMAutomationReportPowerStateChange: Reporting power state change (opcode=2, err=0).
2025-05-31T07:48:15.721Z In(05) vcpu-0 Transitioned vmx/execState/val to poweredOn
2025-05-31T07:48:15.721Z In(05) vcpu-0 Tools: Adding Tools inactivity timer.
2025-05-31T07:48:15.721Z In(05) vmx VNET: MACVNetLinkStateEventHandler: event, up:1, adapter:0
2025-05-31T07:48:15.721Z In(05) vmx VNET: MACVNetLinkStateEventHandler: 'ethernet0' state from 4 to 6.
2025-05-31T07:48:15.747Z In(05) mks MKSControlMgr: connected
2025-05-31T07:48:15.748Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 1.
2025-05-31T07:48:15.748Z In(05) mks SWBWindow: Window 0 Defined: src screenId=-1, src xywh(0, 0, 800, 600) dest xywh(0, 0, 800, 600) pixelScale=1, flags=0x7
2025-05-31T07:48:15.748Z In(05) mks MKS-HWinMux: Backend Switch Queued
2025-05-31T07:48:15.748Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-31T07:48:15.748Z In(05) mks SWBWindow: Window 1 Defined: src screenId=-1, src xywh(0, 0, 800, 600) dest xywh(0, 0, 800, 600) pixelScale=1, flags=0x8
2025-05-31T07:48:15.748Z In(05) mks MKS-HWinMux: Backend Switch Queued
2025-05-31T07:48:15.749Z In(05) mks GDI-Backend: successfully started by HWinMux to do window composition.
2025-05-31T07:48:15.752Z In(05) mks MKS-HWinMux: Started GDI presentation backend.
2025-05-31T07:48:15.754Z In(05) mks MKS-VMDB: VMDB requested a screenshot
2025-05-31T07:48:15.754Z In(05) svga MKSScreenShotMgr: Taking a screenshot
2025-05-31T07:48:15.756Z In(05) vmx [msg.dictionary.badEncodedOutput] Value "Đại Chiến Quốc Mobile LouLx" for variable "displayName" is not valid in the "windows-1252" encoding.
2025-05-31T07:48:15.756Z In(05) vmx Dictionary_WriteToBuffer: upgrading encoding from windows-1252 to UTF-8
2025-05-31T07:48:15.756Z In(05) mks SWBWindow: Window #0 validation failed: no valid host window or host surface.
2025-05-31T07:48:15.756Z In(05) mks SWBVmdb: Destroy SWB Window Id #0 because an invalid MKSWindow definition is received from UI over VMDB.
2025-05-31T07:48:15.756Z In(05) mks SWBWindow: Window 0 Destroyed: src screenId=-1, src xywh(0, 0, 800, 600) dest xywh(0, 0, 800, 600) pixelScale=1, flags=0x7
2025-05-31T07:48:15.756Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2025-05-31T07:48:15.758Z In(05) mks KHBKL: Unable to parse keystring at: ''
2025-05-31T07:48:15.768Z In(05) vmx [msg.dictionary.badEncodedOutput] Value "Đại Chiến Quốc Mobile LouLx" for variable "displayName" is not valid in the "windows-1252" encoding.
2025-05-31T07:48:15.768Z In(05) vmx Dictionary_WriteToBuffer: upgrading encoding from windows-1252 to UTF-8
2025-05-31T07:48:15.768Z In(05) svga SWBScreen: Screen 0 Defined: xywh(0, 0, 640, 480) flags=0x3
2025-05-31T07:48:15.768Z In(05) mks SWBWindow: Window #0 validation failed: no valid host window or host surface.
2025-05-31T07:48:15.775Z In(05) mks SWBWindow: Number of MKSWindows changed: 1 rendering MKSWindow(s) of total 2.
2025-05-31T07:48:15.775Z In(05) mks SWBWindow: Window 0 Defined: src screenId=-1, src xywh(0, 0, 640, 480) dest xywh(0, 0, 640, 480) pixelScale=1, flags=0x7
2025-05-31T07:48:15.801Z In(05) vcpu-0 Syncing WHP TSCs took 22 us. Threshold is 1000 us.
2025-05-31T07:48:15.847Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 6 to 1.
2025-05-31T07:48:15.929Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0) and 0xfe000000(0)
2025-05-31T07:48:15.929Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-31T07:48:15.933Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:15.933Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-31T07:48:16.055Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:16.055Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-31T07:48:16.060Z In(05) vcpu-0 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:16.060Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-31T07:48:16.060Z In(05) vcpu-0 SVGA: Registering IOSpace at 0x1070
2025-05-31T07:48:16.061Z In(05) vcpu-0 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:16.061Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-31T07:48:16.105Z In(05) vcpu-1 CPU reset: soft (mode Emulation)
2025-05-31T07:48:16.120Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-31T07:48:16.127Z In(05) vcpu-0 SVGA: FIFO is already mapped
2025-05-31T07:48:16.282Z In(05) vcpu-0 DISKUTIL: scsi0:0 : geometry=7832/255/63
2025-05-31T07:48:16.282Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=125829120 logical sector size=512
2025-05-31T07:48:16.631Z In(05) vcpu-1 CPU reset: soft (mode Emulation)
2025-05-31T07:48:16.651Z In(05) vcpu-0 BIOS-UUID is 56 4d 90 07 c8 75 9d 8b-18 cd 1f d4 62 17 98 f4
2025-05-31T07:48:16.957Z In(05) svga SWBScreen: Screen 0 Resized: xywh(0, 0, 720, 400) flags=0x3
2025-05-31T07:48:21.222Z In(05) vmx VNET: MACVNetLinkStateTimerHandler: 'ethernet0' state from 1 to 5.
2025-05-31T07:48:23.686Z In(05) vcpu-1 CPU reset: soft (mode Emulation)
2025-05-31T07:48:23.816Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2025-05-31T07:48:23.816Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.816Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:23.816Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2025-05-31T07:48:23.816Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.816Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:23.816Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2025-05-31T07:48:23.816Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.816Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:23.817Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2025-05-31T07:48:23.817Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.817Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:23.817Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2025-05-31T07:48:23.817Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.817Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:23.817Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2025-05-31T07:48:23.817Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.817Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:23.817Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2025-05-31T07:48:23.817Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.817Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:23.818Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2025-05-31T07:48:23.818Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.818Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:23.818Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2025-05-31T07:48:23.818Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.818Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:23.818Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2025-05-31T07:48:23.818Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.818Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:23.818Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2025-05-31T07:48:23.818Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.818Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:23.819Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2025-05-31T07:48:23.819Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.819Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:23.819Z In(05) vcpu-1 SVGA: Unregistering IOSpace at 0x1070
2025-05-31T07:48:23.819Z In(05) vcpu-1 SVGA: Unregistering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.819Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:23.819Z In(05) vcpu-1 SVGA: Registering IOSpace at 0x1070
2025-05-31T07:48:23.819Z In(05) vcpu-1 SVGA: Registering MemSpace at 0xe8000000(0xe8000000) and 0xfe000000(0xfe000000)
2025-05-31T07:48:23.819Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:24.373Z In(05) vcpu-0 VMMouse: CMD Read ID
2025-05-31T07:48:24.643Z In(05) vcpu-0 SCSI0: RESET BUS
2025-05-31T07:48:24.758Z In(05) vcpu-1 SCSI0: RESET BUS
2025-05-31T07:48:24.761Z In(05) vcpu-1 SVGA: FIFO is already mapped
2025-05-31T07:48:24.762Z In(05) svga SVGA hiding SVGA
2025-05-31T07:48:24.769Z In(05) vcpu-1 Guest: vmwgfx: In Tree-Unknown
2025-05-31T07:48:24.769Z In(05) vcpu-1 Guest: vmwgfx: Module Version: 2.15.0
2025-05-31T07:48:24.769Z In(05) svga SVGA enabling SVGA
2025-05-31T07:48:24.769Z In(05) svga SWBScreen: Screen 0 Destroyed: xywh(0, 0, 720, 400) flags=0x3
2025-05-31T07:48:24.769Z In(05) svga SVGA-ScreenMgr: Screen type changed to RegisterMode
2025-05-31T07:48:24.769Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 800, 480) flags=0x2
2025-05-31T07:48:24.783Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 800, 480) flags=0x2
2025-05-31T07:48:24.783Z In(05) svga SVGA-ScreenMgr: Screen type changed to ScreenTarget
2025-05-31T07:48:24.783Z In(05) svga SWBScreen: Screen 1 Defined: xywh(0, 0, 800, 600) flags=0x2
2025-05-31T07:48:24.949Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=125829120 logical sector size=512
2025-05-31T07:48:24.949Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2025-05-31T07:48:24.950Z In(05) vcpu-0 DISKUTIL: scsi0:0 : capacity=125829120 logical sector size=512
2025-05-31T07:48:24.950Z In(05) vcpu-0 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2025-05-31T07:48:24.951Z In(05) vcpu-1 DISKUTIL: scsi0:0 : capacity=125829120 logical sector size=512
2025-05-31T07:48:24.951Z In(05) vcpu-1 SCSI DEVICE (scsi0:0): MODE SENSE(6) for unsupported page 0x8
2025-05-31T07:48:25.209Z In(05) vcpu-1 DDB: "longContentID" = "3e85586939d5d20e90990a25812454a6" (was "deb79e6b7b095f110c341bffe2c95afe")
2025-05-31T07:48:25.398Z In(05) vcpu-0 Tools: Running status rpc handler: 0 => 1.
2025-05-31T07:48:25.398Z In(05) vcpu-0 Tools: Changing running status: 0 => 1.
2025-05-31T07:48:25.398Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 1 (last received 0s ago)
2025-05-31T07:48:25.398Z In(05) vcpu-0 Tools: Removing Tools inactivity timer.
2025-05-31T07:48:46.408Z In(05) vcpu-0 Tools: Tools heartbeat timeout.
2025-05-31T07:48:46.408Z In(05) vcpu-0 Tools: Running status rpc handler: 1 => 0.
2025-05-31T07:48:46.408Z In(05) vcpu-0 Tools: Changing running status: 1 => 0.
2025-05-31T07:48:46.408Z In(05) vcpu-0 Tools: [RunningStatus] Last heartbeat value 1 (last received 21s ago)
2025-05-31T07:48:46.408Z In(05) vcpu-0 TOOLS hostVerifiedSamlToken capability set to FALSE.
2025-05-31T07:49:55.842Z In(05) vmx Vix: [vmxCommands.c:557]: VMAutomation_InitiatePowerOff. Trying hard powerOff
2025-05-31T07:49:55.843Z In(05) vmx Stopping VCPU threads...
2025-05-31T07:49:55.846Z In(05) vmx Stopping MKS/SVGA threads
2025-05-31T07:49:55.846Z In(05) vmx MKSThread: Requesting MKS exit
2025-05-31T07:49:55.846Z In(05) svga MKSThread: SVGA thread is exiting the main loop
2025-05-31T07:49:55.847Z In(05) svga SWBScreen: Screen 1 Destroyed: xywh(0, 0, 800, 600) flags=0x2
2025-05-31T07:49:55.848Z In(05) mks SWBWindow: Window 0 Destroyed: src screenId=-1, src xywh(0, 0, 800, 600) dest xywh(0, 0, 800, 600) pixelScale=1, flags=0x7
2025-05-31T07:49:55.849Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 1.
2025-05-31T07:49:55.849Z In(05) mks SWBWindow: Window 1 Destroyed: src screenId=-1, src xywh(0, 0, 800, 600) dest xywh(0, 0, 800, 600) pixelScale=1, flags=0x8
2025-05-31T07:49:55.849Z In(05) mks MKS-HWinMux: Backend Switch Queued
2025-05-31T07:49:55.850Z In(05) mks SWBWindow: Number of MKSWindows changed: 0 rendering MKSWindow(s) of total 0.
2025-05-31T07:49:55.855Z In(05) vmx MKS/SVGA threads are stopped
2025-05-31T07:49:55.855Z In(05) vmx 
2025-05-31T07:49:55.855Z In(05)+ vmx OvhdMem: Final (Power Off) Overheads
2025-05-31T07:49:55.855Z In(05) vmx                                                       reserved      |          used
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem excluded                                  cur    max    avg |    cur    max    avg
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_MainMem                    :  1048576 1048576      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxText                    :    7936   7936      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxTextLibs                :   17408  17408      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem Total excluded                      :  1073920 1073920      - |      -      -      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem Actual maximum                      :         1073920        |             -
2025-05-31T07:49:55.855Z In(05)+ vmx 
2025-05-31T07:49:55.855Z In(05) vmx                                                       reserved      |          used
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem paged                                     cur    max    avg |    cur    max    avg
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_STATS_vmm                  :       4      4      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_STATS_device               :       2      2      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_SvgaMobFallback            :   98304  98304      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_DiskLibMemUsed             :    3075   3075      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_SvgaSurfaceTable           :       6      6      - |      1      1      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_SvgaSDirtyCache            :      96     96      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_SvgaCursor                 :      10     10      - |     10     10      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_SvgaPPNList                :     130    130      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxGlobals                 :   10240  10240      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxGlobalsLibs             :    3584   3584      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxHeap                    :    8704   8704      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxMks                     :      49     49      - |     17     17      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxMksRenderOps            :     678    678      - |    492    492      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxMks3d                   :   65537  65537      - |      0    469      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxMksScreenTemp           :   69890  69890      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxMksVnc                  :   74936  74936      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxMksScreen               :  131075 131075      - |      0    376      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxMksSVGAVO               :    4096   4096      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxMksSwbCursor            :    4096   4096      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxPhysMemErrPages         :      10     10      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxSLEntryBuf              :     128    128      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VmxThreads                 :   35840  35840      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem Total paged                         :  510490 510490      - |    520   1365      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem Actual maximum                      :         510490        |        510490
2025-05-31T07:49:55.855Z In(05)+ vmx 
2025-05-31T07:49:55.855Z In(05) vmx                                                       reserved      |          used
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem nonpaged                                  cur    max    avg |    cur    max    avg
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_SharedArea                 :       8      8      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_BusMemTraceBitmap          :      37     37      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_PFrame                     :       0   3266      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_BusMemFrame                :    1141   2241      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VIDE_KSEG                  :      16     16      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VGA                        :      64     64      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_BalloonMPN                 :       1      1      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_P2MUpdateBuffer            :       3      3      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_ServicesMPN                :       3      3      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_LocalApic                  :       2      2      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_VBIOS                      :       8      8      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_LSIBIOS                    :       4      4      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_LSIRings                   :       4      4      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_SAS1068BIOS                :       4      4      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_SBIOS                      :      16     16      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_FlashRam                   :     128    128      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_SMM                        :      63     63      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_SVGAFB                     :    1024   1024      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_SVGAMEM                    :      64    512      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_HDAudioReg                 :       3      3      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_EHCIRegister               :       1      1      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_XhciRegister               :       1      1      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_HyperV                     :       2      2      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_ExtCfg                     :       4      4      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_vhvCachedVMCS              :       2      2      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_vhvNestedAPIC              :       2      2      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_LBR                        :       2      2      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_MonWired                   :      53     53      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_NVDC                       :       1      1      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdUser_PCIeMMIOArea               :      70     70      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem Total nonpaged                      :    2731   7545      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem Actual maximum                      :           2731        |          7545
2025-05-31T07:49:55.855Z In(05)+ vmx 
2025-05-31T07:49:55.855Z In(05) vmx                                                       reserved      |          used
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem anonymous                                 cur    max    avg |    cur    max    avg
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_Alloc                       :     196    196      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_BusMemFrame                 :    1090   1147      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_BusMem2MInfo                :      16     16      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_BusMem1GInfo                :       1      1      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_BusMemZapListMPN            :       1      1      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_BusMemPreval                :       8      8      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_MonAS                       :       2      2      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_GuestMem                    :      80     80      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_TC                          :    1026   1026      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_BusMemMonAS                 :       6      6      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_PlatformMonAS               :       9      9      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_HVNuma                      :       4      4      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_HV                          :       2      2      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_HVMSRBitmap                 :       1      1      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_VHVGuestMSRBitmap           :       2      2      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_VHV                         :       6      6      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_Numa                        :      30     30      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_BaseWired                   :      58     58      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_Bootstrap                   :    2300   2300      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_GPhysTraced                 :     463    463      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_GPhysHWMMU                  :    2310   2310      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_GPhysNoTrace                :     266    266      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_PhysMemGart                 :     104    104      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_PhysMemErr                  :       7      7      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem OvhdMon_VProbe                      :       1      1      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem Total anonymous                     :    7989   8046      - |      0      0      -
2025-05-31T07:49:55.855Z In(05) vmx OvhdMem Actual maximum                      :           7989        |          8046
2025-05-31T07:49:55.855Z In(05)+ vmx 
2025-05-31T07:49:55.855Z In(05) vmx VMMEM: Maximum Reservation: 2055MB (MainMem=4096MB)
2025-05-31T07:49:55.855Z In(05) vmx MemSched: BALLOON HIST [0, 1048576]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-31T07:49:55.855Z In(05) vmx MemSched: BALLOON P50 1 P70 1 P90 1 MIN 1048576 MAX 0
2025-05-31T07:49:55.855Z In(05) vmx MemSched: SWAP HIST [0, 1048576]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-31T07:49:55.855Z In(05) vmx MemSched: SWAP P50 1 P70 1 P90 1 MIN 1048576 MAX 0
2025-05-31T07:49:55.855Z In(05) vmx MemSched: LOCK HIST [0, 1048576]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-31T07:49:55.855Z In(05) vmx MemSched: LOCK P50 1 P70 1 P90 1 MIN 1048576 MAX 0
2025-05-31T07:49:55.855Z In(05) vmx MemSched: LOCK_TARGET HIST [0, 1048576]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-31T07:49:55.855Z In(05) vmx MemSched: LOCK_TARGET P50 1 P70 1 P90 1 MIN 1048576 MAX 0
2025-05-31T07:49:55.855Z In(05) vmx MemSched: ACTIVE_PCT HIST [0, 100]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-31T07:49:55.855Z In(05) vmx MemSched: ACTIVE_PCT P50 1 P70 1 P90 1 MIN 100 MAX 0
2025-05-31T07:49:55.855Z In(05) vmx MemSched: NUM_VMS HIST [0, 10]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-31T07:49:55.857Z In(05) vmx MemSched: NUM_VMS P50 1 P70 1 P90 1 MIN 10 MAX 0
2025-05-31T07:49:55.857Z In(05) vmx MemSched: HOSTLOCK HIST [0, 2804479]: 0 0 0 0 0 0 0 0 0 0 0 0
2025-05-31T07:49:55.857Z In(05) vmx MemSched: HOSTLOCK P50 1 P70 1 P90 1 MIN 2804479 MAX 0
2025-05-31T07:49:55.857Z In(05) vmx TOOLS received request in VMX to set option 'enableDnD' -> '0'
2025-05-31T07:49:55.857Z In(05) vmx TOOLS received request in VMX to set option 'copypaste' -> '0'
2025-05-31T07:49:55.922Z In(05) vmx HgfsServerManagerVigorExit: Destroy:
2025-05-31T07:49:55.922Z In(05) vmx ToolsISO: Refreshing imageName for 'centos7-64' (refreshCount=1, lastCount=1).
2025-05-31T07:49:55.923Z In(05) vmx ToolsISO: open of C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig failed: Could not find the file
2025-05-31T07:49:55.923Z In(05) vmx ToolsISO: Unable to read signature file 'C:\Program Files (x86)\VMware\VMware Workstation\isoimages_manifest.txt.sig', ignoring.
2025-05-31T07:49:55.923Z In(05) vmx ToolsISO: Updated cached value for imageName to 'linux.iso'.
2025-05-31T07:49:55.923Z In(05) vmx ToolsISO: Selected Tools ISO 'linux.iso' for 'centos7-64' guest.
2025-05-31T07:49:55.923Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("C:\Program Files (x86)\VMware\VMware Workstation\linux.iso", ...) failed, error: 2
2025-05-31T07:49:55.923Z In(05) vmx TOOLS updated cached value for isoImageExists to 0.
2025-05-31T07:49:55.924Z In(05) vmx Tools: ToolsRunningStatus_Exit, delayedRequest is 0x223B7303FE0
2025-05-31T07:49:55.924Z In(05) vmx Tools: [AppStatus] Last heartbeat value 1 (last received 90s ago)
2025-05-31T07:49:55.924Z In(05) vmx TOOLS: appName=toolbox, oldStatus=0, status=0, guestInitiated=0.
2025-05-31T07:49:55.935Z In(05) vmx SOUNDLIB: Closing Wave sound backend.
2025-05-31T07:49:55.938Z In(05) mks MKSControlMgr: disconnected
2025-05-31T07:49:55.938Z In(05) mks GDI-Backend: stopped by HWinMux to do window composition.
2025-05-31T07:49:55.938Z In(05) mks MKSRenderMain: Stopping BasicOps
2025-05-31T07:49:55.938Z In(05) mks MKSRenderMain: Stopped BasicOps
2025-05-31T07:49:55.941Z In(05) mks MKS PowerOff
2025-05-31T07:49:55.941Z In(05) svga SVGA thread is exiting
2025-05-31T07:49:55.941Z In(05) mks MKS thread is exiting
2025-05-31T07:49:55.941Z Wa(03) vmx Guest: 
2025-05-31T07:49:55.941Z In(05) vmx scsi0:0: numIOs = 10097 numMergedIOs = 1758 numSplitIOs = 271 (13.4%)
2025-05-31T07:49:55.941Z In(05) vmx Closing disk 'scsi0:0'
2025-05-31T07:49:55.945Z In(05) vmx AIOWIN32C: asyncOps=10205 syncOps=114 bufSize=296Kb fixedOps=3981
2025-05-31T07:49:55.945Z In(05) aioCompletion AIO thread processed 10205 completions
2025-05-31T07:49:56.082Z In(05) deviceThread Device thread is exiting
2025-05-31T07:49:56.083Z In(05) vmx Vix: [mainDispatch.c:1172]: VMAutomationPowerOff: Powering off.
2025-05-31T07:49:56.083Z In(05) vmx Win32U_GetFileAttributes: GetFileAttributesExW("D:\Dai-Chien-Quoc-LouLx\Dai-Chien-Quoc-LouLx\LouLx-Game\Server\LouLx-Game.vmpl", ...) failed, error: 2
2025-05-31T07:49:56.083Z In(05) vmx Policy_SavePolicyFile: invalid arguments to function.
2025-05-31T07:49:56.083Z In(05) vmx PolicyVMX_Exit: Could not write out policies: 15.
2025-05-31T07:49:56.083Z In(05) vmx WORKER: asyncOps=1 maxActiveOps=1 maxPending=1 maxCompleted=0
2025-05-31T07:49:56.083Z In(05) PowerNotifyThread PowerNotify thread exiting.
2025-05-31T07:49:56.088Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=1, newAppState=1873, success=1 additionalError=0
2025-05-31T07:49:56.088Z In(05) vmx Vix: [mainDispatch.c:4231]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2025-05-31T07:49:56.088Z In(05) vmx STATSFILE: Amount of dynamic storage used: 656
2025-05-31T07:49:56.088Z In(05) vmx STATSFILE: Number of entries made: 69
2025-05-31T07:49:56.088Z No(00) vmx ConfigDB: Setting cleanShutdown = "TRUE"
2025-05-31T07:49:56.100Z In(05) vmx Vix: [mainDispatch.c:4213]: VMAutomation_ReportPowerOpFinished: statevar=0, newAppState=1870, success=1 additionalError=0
2025-05-31T07:49:56.100Z In(05) vmx Vix: [mainDispatch.c:4231]: VMAutomation: Ignoring ReportPowerOpFinished because the VMX is shutting down.
2025-05-31T07:49:56.100Z In(05) vmx Transitioned vmx/execState/val to poweredOff
2025-05-31T07:49:56.100Z In(05) vmx VMX idle exit
2025-05-31T07:49:56.100Z In(05) vmx WQPoolFreePoll : pollIx = 3, signalHandle = 972
2025-05-31T07:49:56.100Z In(05) vmx Vix: [mainDispatch.c:818]: VMAutomation_LateShutdown()
2025-05-31T07:49:56.100Z In(05) vmx Vix: [mainDispatch.c:773]: VMAutomationCloseListenerSocket. Closing listener socket.
2025-05-31T07:49:56.100Z In(05) vmx Flushing VMX VMDB connections
2025-05-31T07:49:56.100Z In(05) vmx VmdbDbRemoveCnx: Removing Cnx from Db for '/db/connection/#1/'
2025-05-31T07:49:56.100Z In(05) vmx VmdbCnxDisconnect: Disconnect: closed pipe for pub cnx '/db/connection/#1/' (0)
2025-05-31T07:49:56.101Z In(05) vmx VigorTransport_ServerDestroy: server destroyed.
2025-05-31T07:49:56.101Z In(05) vmx WQPoolFreePoll : pollIx = 2, signalHandle = 756
2025-05-31T07:49:56.101Z In(05) vmx WQPoolFreePoll : pollIx = 1, signalHandle = 800
2025-05-31T07:49:56.102Z In(05) vmx VMX exit (0).
2025-05-31T07:49:56.102Z In(05) vmx OBJLIB-LIB: ObjLib cleanup done.
2025-05-31T07:49:56.102Z In(05) vmx AIOMGR-S : stat o=3 r=21 w=0 i=0 br=106992 bw=0
